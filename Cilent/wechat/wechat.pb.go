// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.30.0
// 	protoc        v3.14.0
// source: wechat.proto

package wechat

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type BaseRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SessionKey    []byte  `protobuf:"bytes,1,opt,name=sessionKey" json:"sessionKey,omitempty"`
	Uin           *uint32 `protobuf:"varint,2,opt,name=uin" json:"uin,omitempty"`
	DeviceId      []byte  `protobuf:"bytes,3,opt,name=deviceId" json:"deviceId,omitempty"`
	ClientVersion *int32  `protobuf:"varint,4,opt,name=clientVersion" json:"clientVersion,omitempty"`
	DeviceType    []byte  `protobuf:"bytes,5,opt,name=deviceType" json:"deviceType,omitempty"`
	Scene         *uint32 `protobuf:"varint,6,opt,name=scene" json:"scene,omitempty"`
}

func (x *BaseRequest) Reset() {
	*x = BaseRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wechat_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BaseRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BaseRequest) ProtoMessage() {}

func (x *BaseRequest) ProtoReflect() protoreflect.Message {
	mi := &file_wechat_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BaseRequest.ProtoReflect.Descriptor instead.
func (*BaseRequest) Descriptor() ([]byte, []int) {
	return file_wechat_proto_rawDescGZIP(), []int{0}
}

func (x *BaseRequest) GetSessionKey() []byte {
	if x != nil {
		return x.SessionKey
	}
	return nil
}

func (x *BaseRequest) GetUin() uint32 {
	if x != nil && x.Uin != nil {
		return *x.Uin
	}
	return 0
}

func (x *BaseRequest) GetDeviceId() []byte {
	if x != nil {
		return x.DeviceId
	}
	return nil
}

func (x *BaseRequest) GetClientVersion() int32 {
	if x != nil && x.ClientVersion != nil {
		return *x.ClientVersion
	}
	return 0
}

func (x *BaseRequest) GetDeviceType() []byte {
	if x != nil {
		return x.DeviceType
	}
	return nil
}

func (x *BaseRequest) GetScene() uint32 {
	if x != nil && x.Scene != nil {
		return *x.Scene
	}
	return 0
}

type BaseRequestPlus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 会话key base64字符串
	SessionKey    []byte  `protobuf:"bytes,1,opt,name=session_key,json=sessionKey" json:"session_key,omitempty"`
	Uin           *uint32 `protobuf:"varint,2,opt,name=uin" json:"uin,omitempty"`
	DeviceId      []byte  `protobuf:"bytes,3,opt,name=deviceId" json:"deviceId,omitempty"`
	ClientVersion *int32  `protobuf:"varint,4,opt,name=clientVersion" json:"clientVersion,omitempty"`
	OsType        *string `protobuf:"bytes,5,opt,name=osType" json:"osType,omitempty"`
	Scene         *uint32 `protobuf:"varint,6,opt,name=scene" json:"scene,omitempty"`
}

func (x *BaseRequestPlus) Reset() {
	*x = BaseRequestPlus{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wechat_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BaseRequestPlus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BaseRequestPlus) ProtoMessage() {}

func (x *BaseRequestPlus) ProtoReflect() protoreflect.Message {
	mi := &file_wechat_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BaseRequestPlus.ProtoReflect.Descriptor instead.
func (*BaseRequestPlus) Descriptor() ([]byte, []int) {
	return file_wechat_proto_rawDescGZIP(), []int{1}
}

func (x *BaseRequestPlus) GetSessionKey() []byte {
	if x != nil {
		return x.SessionKey
	}
	return nil
}

func (x *BaseRequestPlus) GetUin() uint32 {
	if x != nil && x.Uin != nil {
		return *x.Uin
	}
	return 0
}

func (x *BaseRequestPlus) GetDeviceId() []byte {
	if x != nil {
		return x.DeviceId
	}
	return nil
}

func (x *BaseRequestPlus) GetClientVersion() int32 {
	if x != nil && x.ClientVersion != nil {
		return *x.ClientVersion
	}
	return 0
}

func (x *BaseRequestPlus) GetOsType() string {
	if x != nil && x.OsType != nil {
		return *x.OsType
	}
	return ""
}

func (x *BaseRequestPlus) GetScene() uint32 {
	if x != nil && x.Scene != nil {
		return *x.Scene
	}
	return 0
}

type SKBuiltinStringT struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	String_ *string `protobuf:"bytes,1,opt,name=string" json:"string,omitempty"`
}

func (x *SKBuiltinStringT) Reset() {
	*x = SKBuiltinStringT{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wechat_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SKBuiltinStringT) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SKBuiltinStringT) ProtoMessage() {}

func (x *SKBuiltinStringT) ProtoReflect() protoreflect.Message {
	mi := &file_wechat_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SKBuiltinStringT.ProtoReflect.Descriptor instead.
func (*SKBuiltinStringT) Descriptor() ([]byte, []int) {
	return file_wechat_proto_rawDescGZIP(), []int{2}
}

func (x *SKBuiltinStringT) GetString_() string {
	if x != nil && x.String_ != nil {
		return *x.String_
	}
	return ""
}

type BaseResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret    *int32            `protobuf:"varint,1,opt,name=ret" json:"ret,omitempty"`
	ErrMsg *SKBuiltinStringT `protobuf:"bytes,2,opt,name=errMsg" json:"errMsg,omitempty"`
}

func (x *BaseResponse) Reset() {
	*x = BaseResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wechat_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BaseResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BaseResponse) ProtoMessage() {}

func (x *BaseResponse) ProtoReflect() protoreflect.Message {
	mi := &file_wechat_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BaseResponse.ProtoReflect.Descriptor instead.
func (*BaseResponse) Descriptor() ([]byte, []int) {
	return file_wechat_proto_rawDescGZIP(), []int{3}
}

func (x *BaseResponse) GetRet() int32 {
	if x != nil && x.Ret != nil {
		return *x.Ret
	}
	return 0
}

func (x *BaseResponse) GetErrMsg() *SKBuiltinStringT {
	if x != nil {
		return x.ErrMsg
	}
	return nil
}

type MMBizJsApiGetUserOpenIdRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BaseRequest *BaseRequest `protobuf:"bytes,1,req,name=BaseRequest" json:"BaseRequest,omitempty"`
	AppId       *string      `protobuf:"bytes,2,opt,name=AppId" json:"AppId,omitempty"`
	BusiId      *string      `protobuf:"bytes,3,opt,name=BusiId" json:"BusiId,omitempty"`
	UserName    *string      `protobuf:"bytes,4,opt,name=UserName" json:"UserName,omitempty"`
}

func (x *MMBizJsApiGetUserOpenIdRequest) Reset() {
	*x = MMBizJsApiGetUserOpenIdRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wechat_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MMBizJsApiGetUserOpenIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MMBizJsApiGetUserOpenIdRequest) ProtoMessage() {}

func (x *MMBizJsApiGetUserOpenIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_wechat_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MMBizJsApiGetUserOpenIdRequest.ProtoReflect.Descriptor instead.
func (*MMBizJsApiGetUserOpenIdRequest) Descriptor() ([]byte, []int) {
	return file_wechat_proto_rawDescGZIP(), []int{4}
}

func (x *MMBizJsApiGetUserOpenIdRequest) GetBaseRequest() *BaseRequest {
	if x != nil {
		return x.BaseRequest
	}
	return nil
}

func (x *MMBizJsApiGetUserOpenIdRequest) GetAppId() string {
	if x != nil && x.AppId != nil {
		return *x.AppId
	}
	return ""
}

func (x *MMBizJsApiGetUserOpenIdRequest) GetBusiId() string {
	if x != nil && x.BusiId != nil {
		return *x.BusiId
	}
	return ""
}

func (x *MMBizJsApiGetUserOpenIdRequest) GetUserName() string {
	if x != nil && x.UserName != nil {
		return *x.UserName
	}
	return ""
}

type MMBizJsApiGetUserOpenIdResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BaseResponse         *BaseResponse `protobuf:"bytes,1,req,name=BaseResponse" json:"BaseResponse,omitempty"`
	Openid               *string       `protobuf:"bytes,2,opt,name=Openid" json:"Openid,omitempty"`
	NickName             *string       `protobuf:"bytes,3,opt,name=NickName" json:"NickName,omitempty"`
	HeadImgUrl           *string       `protobuf:"bytes,4,opt,name=HeadImgUrl" json:"HeadImgUrl,omitempty"`
	Sign                 *string       `protobuf:"bytes,5,opt,name=Sign" json:"Sign,omitempty"`
	FriendRelation       *uint32       `protobuf:"varint,6,opt,name=FriendRelation" json:"FriendRelation,omitempty"`
	XXX_NoUnkeyedLiteral *string       `protobuf:"bytes,7,opt,name=XXX_NoUnkeyedLiteral,json=XXXNoUnkeyedLiteral" json:"XXX_NoUnkeyedLiteral,omitempty"`
	XXXUnrecognized      *string       `protobuf:"bytes,8,opt,name=XXX_unrecognized,json=XXXUnrecognized" json:"XXX_unrecognized,omitempty"`
	XXXSizecache         *string       `protobuf:"bytes,9,opt,name=XXX_sizecache,json=XXXSizecache" json:"XXX_sizecache,omitempty"`
}

func (x *MMBizJsApiGetUserOpenIdResponse) Reset() {
	*x = MMBizJsApiGetUserOpenIdResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wechat_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MMBizJsApiGetUserOpenIdResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MMBizJsApiGetUserOpenIdResponse) ProtoMessage() {}

func (x *MMBizJsApiGetUserOpenIdResponse) ProtoReflect() protoreflect.Message {
	mi := &file_wechat_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MMBizJsApiGetUserOpenIdResponse.ProtoReflect.Descriptor instead.
func (*MMBizJsApiGetUserOpenIdResponse) Descriptor() ([]byte, []int) {
	return file_wechat_proto_rawDescGZIP(), []int{5}
}

func (x *MMBizJsApiGetUserOpenIdResponse) GetBaseResponse() *BaseResponse {
	if x != nil {
		return x.BaseResponse
	}
	return nil
}

func (x *MMBizJsApiGetUserOpenIdResponse) GetOpenid() string {
	if x != nil && x.Openid != nil {
		return *x.Openid
	}
	return ""
}

func (x *MMBizJsApiGetUserOpenIdResponse) GetNickName() string {
	if x != nil && x.NickName != nil {
		return *x.NickName
	}
	return ""
}

func (x *MMBizJsApiGetUserOpenIdResponse) GetHeadImgUrl() string {
	if x != nil && x.HeadImgUrl != nil {
		return *x.HeadImgUrl
	}
	return ""
}

func (x *MMBizJsApiGetUserOpenIdResponse) GetSign() string {
	if x != nil && x.Sign != nil {
		return *x.Sign
	}
	return ""
}

func (x *MMBizJsApiGetUserOpenIdResponse) GetFriendRelation() uint32 {
	if x != nil && x.FriendRelation != nil {
		return *x.FriendRelation
	}
	return 0
}

func (x *MMBizJsApiGetUserOpenIdResponse) GetXXX_NoUnkeyedLiteral() string {
	if x != nil && x.XXX_NoUnkeyedLiteral != nil {
		return *x.XXX_NoUnkeyedLiteral
	}
	return ""
}

func (x *MMBizJsApiGetUserOpenIdResponse) GetXXXUnrecognized() string {
	if x != nil && x.XXXUnrecognized != nil {
		return *x.XXXUnrecognized
	}
	return ""
}

func (x *MMBizJsApiGetUserOpenIdResponse) GetXXXSizecache() string {
	if x != nil && x.XXXSizecache != nil {
		return *x.XXXSizecache
	}
	return ""
}

type BufferT struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ILen   *uint32 `protobuf:"varint,1,req,name=iLen" json:"iLen,omitempty"`
	Buffer []byte  `protobuf:"bytes,2,opt,name=Buffer" json:"Buffer,omitempty"`
}

func (x *BufferT) Reset() {
	*x = BufferT{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wechat_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BufferT) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BufferT) ProtoMessage() {}

func (x *BufferT) ProtoReflect() protoreflect.Message {
	mi := &file_wechat_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BufferT.ProtoReflect.Descriptor instead.
func (*BufferT) Descriptor() ([]byte, []int) {
	return file_wechat_proto_rawDescGZIP(), []int{6}
}

func (x *BufferT) GetILen() uint32 {
	if x != nil && x.ILen != nil {
		return *x.ILen
	}
	return 0
}

func (x *BufferT) GetBuffer() []byte {
	if x != nil {
		return x.Buffer
	}
	return nil
}

type EcdhPacket struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type         *uint32  `protobuf:"varint,1,req,name=Type" json:"Type,omitempty"`                //固定为1
	Key          *BufferT `protobuf:"bytes,2,req,name=Key" json:"Key,omitempty"`                   //第5步生成的publickey
	Token        []byte   `protobuf:"bytes,3,req,name=Token" json:"Token,omitempty"`               //第8步结果
	Url          *string  `protobuf:"bytes,4,opt,name=Url" json:"Url,omitempty"`                   //空串
	ProtobufData []byte   `protobuf:"bytes,5,opt,name=ProtobufData" json:"ProtobufData,omitempty"` //第10步结果
}

func (x *EcdhPacket) Reset() {
	*x = EcdhPacket{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wechat_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EcdhPacket) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EcdhPacket) ProtoMessage() {}

func (x *EcdhPacket) ProtoReflect() protoreflect.Message {
	mi := &file_wechat_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EcdhPacket.ProtoReflect.Descriptor instead.
func (*EcdhPacket) Descriptor() ([]byte, []int) {
	return file_wechat_proto_rawDescGZIP(), []int{7}
}

func (x *EcdhPacket) GetType() uint32 {
	if x != nil && x.Type != nil {
		return *x.Type
	}
	return 0
}

func (x *EcdhPacket) GetKey() *BufferT {
	if x != nil {
		return x.Key
	}
	return nil
}

func (x *EcdhPacket) GetToken() []byte {
	if x != nil {
		return x.Token
	}
	return nil
}

func (x *EcdhPacket) GetUrl() string {
	if x != nil && x.Url != nil {
		return *x.Url
	}
	return ""
}

func (x *EcdhPacket) GetProtobufData() []byte {
	if x != nil {
		return x.ProtobufData
	}
	return nil
}

type HybridDecryptResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Key          *BufferT `protobuf:"bytes,1,req,name=Key" json:"Key,omitempty"` //第5步生成的publickey
	Type         *uint32  `protobuf:"varint,2,req,name=Type" json:"Type,omitempty"`
	ProtobufData []byte   `protobuf:"bytes,3,req,name=ProtobufData" json:"ProtobufData,omitempty"` //第8步结果
	Token        []byte   `protobuf:"bytes,4,opt,name=token" json:"token,omitempty"`               //空串
}

func (x *HybridDecryptResponse) Reset() {
	*x = HybridDecryptResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wechat_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HybridDecryptResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HybridDecryptResponse) ProtoMessage() {}

func (x *HybridDecryptResponse) ProtoReflect() protoreflect.Message {
	mi := &file_wechat_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HybridDecryptResponse.ProtoReflect.Descriptor instead.
func (*HybridDecryptResponse) Descriptor() ([]byte, []int) {
	return file_wechat_proto_rawDescGZIP(), []int{8}
}

func (x *HybridDecryptResponse) GetKey() *BufferT {
	if x != nil {
		return x.Key
	}
	return nil
}

func (x *HybridDecryptResponse) GetType() uint32 {
	if x != nil && x.Type != nil {
		return *x.Type
	}
	return 0
}

func (x *HybridDecryptResponse) GetProtobufData() []byte {
	if x != nil {
		return x.ProtobufData
	}
	return nil
}

func (x *HybridDecryptResponse) GetToken() []byte {
	if x != nil {
		return x.Token
	}
	return nil
}

type HybridEcdhRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type                *int32   `protobuf:"varint,1,opt,name=type" json:"type,omitempty"`
	SecECDHKey          *BufferT `protobuf:"bytes,2,opt,name=SecECDHKey" json:"SecECDHKey,omitempty"`
	Randomkeydata       []byte   `protobuf:"bytes,3,opt,name=randomkeydata" json:"randomkeydata,omitempty"`
	Randomkeyextenddata []byte   `protobuf:"bytes,4,opt,name=randomkeyextenddata" json:"randomkeyextenddata,omitempty"`
	Encyptdata          []byte   `protobuf:"bytes,5,opt,name=encyptdata" json:"encyptdata,omitempty"`
}

func (x *HybridEcdhRequest) Reset() {
	*x = HybridEcdhRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wechat_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HybridEcdhRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HybridEcdhRequest) ProtoMessage() {}

func (x *HybridEcdhRequest) ProtoReflect() protoreflect.Message {
	mi := &file_wechat_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HybridEcdhRequest.ProtoReflect.Descriptor instead.
func (*HybridEcdhRequest) Descriptor() ([]byte, []int) {
	return file_wechat_proto_rawDescGZIP(), []int{9}
}

func (x *HybridEcdhRequest) GetType() int32 {
	if x != nil && x.Type != nil {
		return *x.Type
	}
	return 0
}

func (x *HybridEcdhRequest) GetSecECDHKey() *BufferT {
	if x != nil {
		return x.SecECDHKey
	}
	return nil
}

func (x *HybridEcdhRequest) GetRandomkeydata() []byte {
	if x != nil {
		return x.Randomkeydata
	}
	return nil
}

func (x *HybridEcdhRequest) GetRandomkeyextenddata() []byte {
	if x != nil {
		return x.Randomkeyextenddata
	}
	return nil
}

func (x *HybridEcdhRequest) GetEncyptdata() []byte {
	if x != nil {
		return x.Encyptdata
	}
	return nil
}

type HybridEcdhResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SecECDHKey          *BufferT `protobuf:"bytes,1,opt,name=SecECDHKey" json:"SecECDHKey,omitempty"`
	Type                *int32   `protobuf:"varint,2,opt,name=type" json:"type,omitempty"`
	Decryptdata         []byte   `protobuf:"bytes,3,opt,name=decryptdata" json:"decryptdata,omitempty"`
	Randomkeyextenddata []byte   `protobuf:"bytes,4,opt,name=randomkeyextenddata" json:"randomkeyextenddata,omitempty"`
}

func (x *HybridEcdhResponse) Reset() {
	*x = HybridEcdhResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wechat_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HybridEcdhResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HybridEcdhResponse) ProtoMessage() {}

func (x *HybridEcdhResponse) ProtoReflect() protoreflect.Message {
	mi := &file_wechat_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HybridEcdhResponse.ProtoReflect.Descriptor instead.
func (*HybridEcdhResponse) Descriptor() ([]byte, []int) {
	return file_wechat_proto_rawDescGZIP(), []int{10}
}

func (x *HybridEcdhResponse) GetSecECDHKey() *BufferT {
	if x != nil {
		return x.SecECDHKey
	}
	return nil
}

func (x *HybridEcdhResponse) GetType() int32 {
	if x != nil && x.Type != nil {
		return *x.Type
	}
	return 0
}

func (x *HybridEcdhResponse) GetDecryptdata() []byte {
	if x != nil {
		return x.Decryptdata
	}
	return nil
}

func (x *HybridEcdhResponse) GetRandomkeyextenddata() []byte {
	if x != nil {
		return x.Randomkeyextenddata
	}
	return nil
}

type ECDHKey struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Nid *uint32           `protobuf:"varint,1,opt,name=nid" json:"nid,omitempty"`
	Key *SKBuiltinString_ `protobuf:"bytes,2,opt,name=key" json:"key,omitempty"`
}

func (x *ECDHKey) Reset() {
	*x = ECDHKey{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wechat_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ECDHKey) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ECDHKey) ProtoMessage() {}

func (x *ECDHKey) ProtoReflect() protoreflect.Message {
	mi := &file_wechat_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ECDHKey.ProtoReflect.Descriptor instead.
func (*ECDHKey) Descriptor() ([]byte, []int) {
	return file_wechat_proto_rawDescGZIP(), []int{11}
}

func (x *ECDHKey) GetNid() uint32 {
	if x != nil && x.Nid != nil {
		return *x.Nid
	}
	return 0
}

func (x *ECDHKey) GetKey() *SKBuiltinString_ {
	if x != nil {
		return x.Key
	}
	return nil
}

type SKBuiltinString_ struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Len *uint32 `protobuf:"varint,1,opt,name=len" json:"len,omitempty"`
	// base64字符串
	Buffer []byte `protobuf:"bytes,2,opt,name=buffer" json:"buffer,omitempty"`
}

func (x *SKBuiltinString_) Reset() {
	*x = SKBuiltinString_{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wechat_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SKBuiltinString_) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SKBuiltinString_) ProtoMessage() {}

func (x *SKBuiltinString_) ProtoReflect() protoreflect.Message {
	mi := &file_wechat_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SKBuiltinString_.ProtoReflect.Descriptor instead.
func (*SKBuiltinString_) Descriptor() ([]byte, []int) {
	return file_wechat_proto_rawDescGZIP(), []int{12}
}

func (x *SKBuiltinString_) GetLen() uint32 {
	if x != nil && x.Len != nil {
		return *x.Len
	}
	return 0
}

func (x *SKBuiltinString_) GetBuffer() []byte {
	if x != nil {
		return x.Buffer
	}
	return nil
}

type ManualAuthRsaReqData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RandomEncryKey *SKBuiltinString_ `protobuf:"bytes,1,opt,name=randomEncryKey" json:"randomEncryKey,omitempty"`
	CliPubEcdhkey  *ECDHKey          `protobuf:"bytes,2,opt,name=cliPubEcdhkey" json:"cliPubEcdhkey,omitempty"`
	UserName       *string           `protobuf:"bytes,3,opt,name=userName" json:"userName,omitempty"`
	Pwd            *string           `protobuf:"bytes,4,opt,name=pwd" json:"pwd,omitempty"`
	Pwd2           *string           `protobuf:"bytes,5,opt,name=pwd2" json:"pwd2,omitempty"`
}

func (x *ManualAuthRsaReqData) Reset() {
	*x = ManualAuthRsaReqData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wechat_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ManualAuthRsaReqData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ManualAuthRsaReqData) ProtoMessage() {}

func (x *ManualAuthRsaReqData) ProtoReflect() protoreflect.Message {
	mi := &file_wechat_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ManualAuthRsaReqData.ProtoReflect.Descriptor instead.
func (*ManualAuthRsaReqData) Descriptor() ([]byte, []int) {
	return file_wechat_proto_rawDescGZIP(), []int{13}
}

func (x *ManualAuthRsaReqData) GetRandomEncryKey() *SKBuiltinString_ {
	if x != nil {
		return x.RandomEncryKey
	}
	return nil
}

func (x *ManualAuthRsaReqData) GetCliPubEcdhkey() *ECDHKey {
	if x != nil {
		return x.CliPubEcdhkey
	}
	return nil
}

func (x *ManualAuthRsaReqData) GetUserName() string {
	if x != nil && x.UserName != nil {
		return *x.UserName
	}
	return ""
}

func (x *ManualAuthRsaReqData) GetPwd() string {
	if x != nil && x.Pwd != nil {
		return *x.Pwd
	}
	return ""
}

func (x *ManualAuthRsaReqData) GetPwd2() string {
	if x != nil && x.Pwd2 != nil {
		return *x.Pwd2
	}
	return ""
}

type WTLoginImgReqInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ImgSid        *string           `protobuf:"bytes,1,opt,name=img_sid,json=imgSid" json:"img_sid,omitempty"`
	ImgCode       *string           `protobuf:"bytes,2,opt,name=img_code,json=imgCode" json:"img_code,omitempty"`
	ImgEncryptKey *string           `protobuf:"bytes,3,opt,name=img_encrypt_key,json=imgEncryptKey" json:"img_encrypt_key,omitempty"`
	Ksid          *SKBuiltinString_ `protobuf:"bytes,4,opt,name=ksid" json:"ksid,omitempty"`
}

func (x *WTLoginImgReqInfo) Reset() {
	*x = WTLoginImgReqInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wechat_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WTLoginImgReqInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WTLoginImgReqInfo) ProtoMessage() {}

func (x *WTLoginImgReqInfo) ProtoReflect() protoreflect.Message {
	mi := &file_wechat_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WTLoginImgReqInfo.ProtoReflect.Descriptor instead.
func (*WTLoginImgReqInfo) Descriptor() ([]byte, []int) {
	return file_wechat_proto_rawDescGZIP(), []int{14}
}

func (x *WTLoginImgReqInfo) GetImgSid() string {
	if x != nil && x.ImgSid != nil {
		return *x.ImgSid
	}
	return ""
}

func (x *WTLoginImgReqInfo) GetImgCode() string {
	if x != nil && x.ImgCode != nil {
		return *x.ImgCode
	}
	return ""
}

func (x *WTLoginImgReqInfo) GetImgEncryptKey() string {
	if x != nil && x.ImgEncryptKey != nil {
		return *x.ImgEncryptKey
	}
	return ""
}

func (x *WTLoginImgReqInfo) GetKsid() *SKBuiltinString_ {
	if x != nil {
		return x.Ksid
	}
	return nil
}

type WxVerifyCodeReqInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	VerifySignature *string `protobuf:"bytes,1,opt,name=verify_signature,json=verifySignature" json:"verify_signature,omitempty"`
	VerifyContent   *string `protobuf:"bytes,2,opt,name=verify_content,json=verifyContent" json:"verify_content,omitempty"`
}

func (x *WxVerifyCodeReqInfo) Reset() {
	*x = WxVerifyCodeReqInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wechat_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WxVerifyCodeReqInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WxVerifyCodeReqInfo) ProtoMessage() {}

func (x *WxVerifyCodeReqInfo) ProtoReflect() protoreflect.Message {
	mi := &file_wechat_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WxVerifyCodeReqInfo.ProtoReflect.Descriptor instead.
func (*WxVerifyCodeReqInfo) Descriptor() ([]byte, []int) {
	return file_wechat_proto_rawDescGZIP(), []int{15}
}

func (x *WxVerifyCodeReqInfo) GetVerifySignature() string {
	if x != nil && x.VerifySignature != nil {
		return *x.VerifySignature
	}
	return ""
}

func (x *WxVerifyCodeReqInfo) GetVerifyContent() string {
	if x != nil && x.VerifyContent != nil {
		return *x.VerifyContent
	}
	return ""
}

type BaseAuthReqInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WtLoginReqBuff      *SKBuiltinString_    `protobuf:"bytes,1,opt,name=wt_login_req_buff,json=wtLoginReqBuff" json:"wt_login_req_buff,omitempty"`
	WtLoginImgReqInfo   *WTLoginImgReqInfo   `protobuf:"bytes,2,opt,name=wt_login_img_req_info,json=wtLoginImgReqInfo" json:"wt_login_img_req_info,omitempty"`
	WxVerifyCodeReqInfo *WxVerifyCodeReqInfo `protobuf:"bytes,3,opt,name=wx_verify_code_req_info,json=wxVerifyCodeReqInfo" json:"wx_verify_code_req_info,omitempty"`
	ClidbEncryptKey     *SKBuiltinString_    `protobuf:"bytes,4,opt,name=clidb_encrypt_key,json=clidbEncryptKey" json:"clidb_encrypt_key,omitempty"`
	ClidbEncryptInfo    *SKBuiltinString_    `protobuf:"bytes,5,opt,name=clidb_encrypt_info,json=clidbEncryptInfo" json:"clidb_encrypt_info,omitempty"`
	AuthReqFlag         *uint32              `protobuf:"varint,6,opt,name=auth_req_flag,json=authReqFlag" json:"auth_req_flag,omitempty"`
	AuthTicket          *string              `protobuf:"bytes,7,opt,name=auth_ticket,json=authTicket" json:"auth_ticket,omitempty"`
}

func (x *BaseAuthReqInfo) Reset() {
	*x = BaseAuthReqInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wechat_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BaseAuthReqInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BaseAuthReqInfo) ProtoMessage() {}

func (x *BaseAuthReqInfo) ProtoReflect() protoreflect.Message {
	mi := &file_wechat_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BaseAuthReqInfo.ProtoReflect.Descriptor instead.
func (*BaseAuthReqInfo) Descriptor() ([]byte, []int) {
	return file_wechat_proto_rawDescGZIP(), []int{16}
}

func (x *BaseAuthReqInfo) GetWtLoginReqBuff() *SKBuiltinString_ {
	if x != nil {
		return x.WtLoginReqBuff
	}
	return nil
}

func (x *BaseAuthReqInfo) GetWtLoginImgReqInfo() *WTLoginImgReqInfo {
	if x != nil {
		return x.WtLoginImgReqInfo
	}
	return nil
}

func (x *BaseAuthReqInfo) GetWxVerifyCodeReqInfo() *WxVerifyCodeReqInfo {
	if x != nil {
		return x.WxVerifyCodeReqInfo
	}
	return nil
}

func (x *BaseAuthReqInfo) GetClidbEncryptKey() *SKBuiltinString_ {
	if x != nil {
		return x.ClidbEncryptKey
	}
	return nil
}

func (x *BaseAuthReqInfo) GetClidbEncryptInfo() *SKBuiltinString_ {
	if x != nil {
		return x.ClidbEncryptInfo
	}
	return nil
}

func (x *BaseAuthReqInfo) GetAuthReqFlag() uint32 {
	if x != nil && x.AuthReqFlag != nil {
		return *x.AuthReqFlag
	}
	return 0
}

func (x *BaseAuthReqInfo) GetAuthTicket() string {
	if x != nil && x.AuthTicket != nil {
		return *x.AuthTicket
	}
	return ""
}

type ManualAuthAesReqData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BaseRequest     *BaseRequest      `protobuf:"bytes,1,opt,name=baseRequest" json:"baseRequest,omitempty"`
	BaseReqInfo     *BaseAuthReqInfo  `protobuf:"bytes,2,opt,name=baseReqInfo" json:"baseReqInfo,omitempty"`
	Imei            *string           `protobuf:"bytes,3,opt,name=imei" json:"imei,omitempty"`
	SoftType        *string           `protobuf:"bytes,4,opt,name=softType" json:"softType,omitempty"`
	BuiltinIpseq    *uint32           `protobuf:"varint,5,opt,name=builtinIpseq" json:"builtinIpseq,omitempty"`
	ClientSeqId     *string           `protobuf:"bytes,6,opt,name=clientSeqId" json:"clientSeqId,omitempty"`
	Signature       *string           `protobuf:"bytes,7,opt,name=signature" json:"signature,omitempty"`
	DeviceName      *string           `protobuf:"bytes,8,opt,name=deviceName" json:"deviceName,omitempty"`
	DeviceType      *string           `protobuf:"bytes,9,opt,name=deviceType" json:"deviceType,omitempty"`
	Language        *string           `protobuf:"bytes,10,opt,name=language" json:"language,omitempty"`
	TimeZone        *string           `protobuf:"bytes,11,opt,name=timeZone" json:"timeZone,omitempty"`
	Channel         *int32            `protobuf:"varint,13,opt,name=channel" json:"channel,omitempty"`
	TimeStamp       *uint32           `protobuf:"varint,14,opt,name=timeStamp" json:"timeStamp,omitempty"`
	DeviceBrand     *string           `protobuf:"bytes,15,opt,name=deviceBrand" json:"deviceBrand,omitempty"`
	DeviceModel     *string           `protobuf:"bytes,16,opt,name=deviceModel" json:"deviceModel,omitempty"`
	Ostype          *string           `protobuf:"bytes,17,opt,name=ostype" json:"ostype,omitempty"`
	RealCountry     *string           `protobuf:"bytes,18,opt,name=realCountry" json:"realCountry,omitempty"`
	BundleId        *string           `protobuf:"bytes,19,opt,name=bundleId" json:"bundleId,omitempty"`
	AdSource        *string           `protobuf:"bytes,20,opt,name=adSource" json:"adSource,omitempty"`
	IphoneVer       *string           `protobuf:"bytes,21,opt,name=iphoneVer" json:"iphoneVer,omitempty"`
	InputType       *uint32           `protobuf:"varint,22,opt,name=inputType" json:"inputType,omitempty"`
	ClientCheckData *SKBuiltinString_ `protobuf:"bytes,23,opt,name=clientCheckData" json:"clientCheckData,omitempty"`
	ExtSpamInfo     *SKBuiltinString_ `protobuf:"bytes,24,opt,name=extSpamInfo" json:"extSpamInfo,omitempty"`
}

func (x *ManualAuthAesReqData) Reset() {
	*x = ManualAuthAesReqData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wechat_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ManualAuthAesReqData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ManualAuthAesReqData) ProtoMessage() {}

func (x *ManualAuthAesReqData) ProtoReflect() protoreflect.Message {
	mi := &file_wechat_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ManualAuthAesReqData.ProtoReflect.Descriptor instead.
func (*ManualAuthAesReqData) Descriptor() ([]byte, []int) {
	return file_wechat_proto_rawDescGZIP(), []int{17}
}

func (x *ManualAuthAesReqData) GetBaseRequest() *BaseRequest {
	if x != nil {
		return x.BaseRequest
	}
	return nil
}

func (x *ManualAuthAesReqData) GetBaseReqInfo() *BaseAuthReqInfo {
	if x != nil {
		return x.BaseReqInfo
	}
	return nil
}

func (x *ManualAuthAesReqData) GetImei() string {
	if x != nil && x.Imei != nil {
		return *x.Imei
	}
	return ""
}

func (x *ManualAuthAesReqData) GetSoftType() string {
	if x != nil && x.SoftType != nil {
		return *x.SoftType
	}
	return ""
}

func (x *ManualAuthAesReqData) GetBuiltinIpseq() uint32 {
	if x != nil && x.BuiltinIpseq != nil {
		return *x.BuiltinIpseq
	}
	return 0
}

func (x *ManualAuthAesReqData) GetClientSeqId() string {
	if x != nil && x.ClientSeqId != nil {
		return *x.ClientSeqId
	}
	return ""
}

func (x *ManualAuthAesReqData) GetSignature() string {
	if x != nil && x.Signature != nil {
		return *x.Signature
	}
	return ""
}

func (x *ManualAuthAesReqData) GetDeviceName() string {
	if x != nil && x.DeviceName != nil {
		return *x.DeviceName
	}
	return ""
}

func (x *ManualAuthAesReqData) GetDeviceType() string {
	if x != nil && x.DeviceType != nil {
		return *x.DeviceType
	}
	return ""
}

func (x *ManualAuthAesReqData) GetLanguage() string {
	if x != nil && x.Language != nil {
		return *x.Language
	}
	return ""
}

func (x *ManualAuthAesReqData) GetTimeZone() string {
	if x != nil && x.TimeZone != nil {
		return *x.TimeZone
	}
	return ""
}

func (x *ManualAuthAesReqData) GetChannel() int32 {
	if x != nil && x.Channel != nil {
		return *x.Channel
	}
	return 0
}

func (x *ManualAuthAesReqData) GetTimeStamp() uint32 {
	if x != nil && x.TimeStamp != nil {
		return *x.TimeStamp
	}
	return 0
}

func (x *ManualAuthAesReqData) GetDeviceBrand() string {
	if x != nil && x.DeviceBrand != nil {
		return *x.DeviceBrand
	}
	return ""
}

func (x *ManualAuthAesReqData) GetDeviceModel() string {
	if x != nil && x.DeviceModel != nil {
		return *x.DeviceModel
	}
	return ""
}

func (x *ManualAuthAesReqData) GetOstype() string {
	if x != nil && x.Ostype != nil {
		return *x.Ostype
	}
	return ""
}

func (x *ManualAuthAesReqData) GetRealCountry() string {
	if x != nil && x.RealCountry != nil {
		return *x.RealCountry
	}
	return ""
}

func (x *ManualAuthAesReqData) GetBundleId() string {
	if x != nil && x.BundleId != nil {
		return *x.BundleId
	}
	return ""
}

func (x *ManualAuthAesReqData) GetAdSource() string {
	if x != nil && x.AdSource != nil {
		return *x.AdSource
	}
	return ""
}

func (x *ManualAuthAesReqData) GetIphoneVer() string {
	if x != nil && x.IphoneVer != nil {
		return *x.IphoneVer
	}
	return ""
}

func (x *ManualAuthAesReqData) GetInputType() uint32 {
	if x != nil && x.InputType != nil {
		return *x.InputType
	}
	return 0
}

func (x *ManualAuthAesReqData) GetClientCheckData() *SKBuiltinString_ {
	if x != nil {
		return x.ClientCheckData
	}
	return nil
}

func (x *ManualAuthAesReqData) GetExtSpamInfo() *SKBuiltinString_ {
	if x != nil {
		return x.ExtSpamInfo
	}
	return nil
}

type TrustSoftData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SoftConfig *string `protobuf:"bytes,1,opt,name=SoftConfig" json:"SoftConfig,omitempty"`
	SoftData   []byte  `protobuf:"bytes,2,opt,name=SoftData" json:"SoftData,omitempty"`
}

func (x *TrustSoftData) Reset() {
	*x = TrustSoftData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wechat_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TrustSoftData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrustSoftData) ProtoMessage() {}

func (x *TrustSoftData) ProtoReflect() protoreflect.Message {
	mi := &file_wechat_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrustSoftData.ProtoReflect.Descriptor instead.
func (*TrustSoftData) Descriptor() ([]byte, []int) {
	return file_wechat_proto_rawDescGZIP(), []int{18}
}

func (x *TrustSoftData) GetSoftConfig() string {
	if x != nil && x.SoftConfig != nil {
		return *x.SoftConfig
	}
	return ""
}

func (x *TrustSoftData) GetSoftData() []byte {
	if x != nil {
		return x.SoftData
	}
	return nil
}

type TrustResponseData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SoftData    *TrustSoftData `protobuf:"bytes,2,opt,name=SoftData" json:"SoftData,omitempty"`
	DeviceToken *string        `protobuf:"bytes,3,opt,name=DeviceToken" json:"DeviceToken,omitempty"`
	Timestamp   *uint64        `protobuf:"varint,4,opt,name=Timestamp" json:"Timestamp,omitempty"`
}

func (x *TrustResponseData) Reset() {
	*x = TrustResponseData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wechat_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TrustResponseData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrustResponseData) ProtoMessage() {}

func (x *TrustResponseData) ProtoReflect() protoreflect.Message {
	mi := &file_wechat_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrustResponseData.ProtoReflect.Descriptor instead.
func (*TrustResponseData) Descriptor() ([]byte, []int) {
	return file_wechat_proto_rawDescGZIP(), []int{19}
}

func (x *TrustResponseData) GetSoftData() *TrustSoftData {
	if x != nil {
		return x.SoftData
	}
	return nil
}

func (x *TrustResponseData) GetDeviceToken() string {
	if x != nil && x.DeviceToken != nil {
		return *x.DeviceToken
	}
	return ""
}

func (x *TrustResponseData) GetTimestamp() uint64 {
	if x != nil && x.Timestamp != nil {
		return *x.Timestamp
	}
	return 0
}

type TrustResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BaseResponse      *BaseResponse      `protobuf:"bytes,1,req,name=BaseResponse" json:"BaseResponse,omitempty"`
	TrustResponseData *TrustResponseData `protobuf:"bytes,2,opt,name=TrustResponseData" json:"TrustResponseData,omitempty"`
}

func (x *TrustResp) Reset() {
	*x = TrustResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wechat_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TrustResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrustResp) ProtoMessage() {}

func (x *TrustResp) ProtoReflect() protoreflect.Message {
	mi := &file_wechat_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrustResp.ProtoReflect.Descriptor instead.
func (*TrustResp) Descriptor() ([]byte, []int) {
	return file_wechat_proto_rawDescGZIP(), []int{20}
}

func (x *TrustResp) GetBaseResponse() *BaseResponse {
	if x != nil {
		return x.BaseResponse
	}
	return nil
}

func (x *TrustResp) GetTrustResponseData() *TrustResponseData {
	if x != nil {
		return x.TrustResponseData
	}
	return nil
}

type AutoAuthKey struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	EncryptKey *BufferT `protobuf:"bytes,1,req,name=EncryptKey" json:"EncryptKey,omitempty"`
	Key        *BufferT `protobuf:"bytes,2,req,name=Key" json:"Key,omitempty"`
}

func (x *AutoAuthKey) Reset() {
	*x = AutoAuthKey{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wechat_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AutoAuthKey) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AutoAuthKey) ProtoMessage() {}

func (x *AutoAuthKey) ProtoReflect() protoreflect.Message {
	mi := &file_wechat_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AutoAuthKey.ProtoReflect.Descriptor instead.
func (*AutoAuthKey) Descriptor() ([]byte, []int) {
	return file_wechat_proto_rawDescGZIP(), []int{21}
}

func (x *AutoAuthKey) GetEncryptKey() *BufferT {
	if x != nil {
		return x.EncryptKey
	}
	return nil
}

func (x *AutoAuthKey) GetKey() *BufferT {
	if x != nil {
		return x.Key
	}
	return nil
}

type AutoAuthRsaReqData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AesEncryptKey *SKBuiltinString_ `protobuf:"bytes,2,opt,name=aes_encrypt_key,json=aesEncryptKey" json:"aes_encrypt_key,omitempty"`
	PubEcdhKey    *ECDHKey          `protobuf:"bytes,3,opt,name=pubEcdhKey" json:"pubEcdhKey,omitempty"`
}

func (x *AutoAuthRsaReqData) Reset() {
	*x = AutoAuthRsaReqData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wechat_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AutoAuthRsaReqData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AutoAuthRsaReqData) ProtoMessage() {}

func (x *AutoAuthRsaReqData) ProtoReflect() protoreflect.Message {
	mi := &file_wechat_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AutoAuthRsaReqData.ProtoReflect.Descriptor instead.
func (*AutoAuthRsaReqData) Descriptor() ([]byte, []int) {
	return file_wechat_proto_rawDescGZIP(), []int{22}
}

func (x *AutoAuthRsaReqData) GetAesEncryptKey() *SKBuiltinString_ {
	if x != nil {
		return x.AesEncryptKey
	}
	return nil
}

func (x *AutoAuthRsaReqData) GetPubEcdhKey() *ECDHKey {
	if x != nil {
		return x.PubEcdhKey
	}
	return nil
}

type AutoAuthAesReqData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BaseRequest     *BaseRequestPlus  `protobuf:"bytes,1,opt,name=base_request,json=baseRequest" json:"base_request,omitempty"`
	BaseReqInfo     *BaseAuthReqInfo  `protobuf:"bytes,2,opt,name=base_req_info,json=baseReqInfo" json:"base_req_info,omitempty"`
	AutoAuthKey     *SKBuiltinString_ `protobuf:"bytes,3,opt,name=auto_auth_key,json=autoAuthKey" json:"auto_auth_key,omitempty"`
	Imei            *string           `protobuf:"bytes,4,opt,name=imei" json:"imei,omitempty"`
	SoftType        *string           `protobuf:"bytes,5,opt,name=soft_type,json=softType" json:"soft_type,omitempty"`
	BuiltinIpSeq    *uint32           `protobuf:"varint,6,opt,name=builtin_ip_seq,json=builtinIpSeq" json:"builtin_ip_seq,omitempty"`
	ClientSeqId     *string           `protobuf:"bytes,7,opt,name=client_seq_id,json=clientSeqId" json:"client_seq_id,omitempty"`
	Signature       *string           `protobuf:"bytes,8,opt,name=signature" json:"signature,omitempty"`
	DeviceName      *string           `protobuf:"bytes,9,opt,name=device_name,json=deviceName" json:"device_name,omitempty"`
	DeviceType      *string           `protobuf:"bytes,10,opt,name=deviceType" json:"deviceType,omitempty"`
	Language        *string           `protobuf:"bytes,11,opt,name=language" json:"language,omitempty"`
	TimeZone        *string           `protobuf:"bytes,12,opt,name=time_zone,json=timeZone" json:"time_zone,omitempty"`
	Channel         *uint32           `protobuf:"varint,13,opt,name=channel" json:"channel,omitempty"`
	ClientCheckData *SKBuiltinString_ `protobuf:"bytes,14,opt,name=clientCheckData" json:"clientCheckData,omitempty"`
	ExtSpamInfo     *SKBuiltinString_ `protobuf:"bytes,15,opt,name=extSpamInfo" json:"extSpamInfo,omitempty"`
}

func (x *AutoAuthAesReqData) Reset() {
	*x = AutoAuthAesReqData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wechat_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AutoAuthAesReqData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AutoAuthAesReqData) ProtoMessage() {}

func (x *AutoAuthAesReqData) ProtoReflect() protoreflect.Message {
	mi := &file_wechat_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AutoAuthAesReqData.ProtoReflect.Descriptor instead.
func (*AutoAuthAesReqData) Descriptor() ([]byte, []int) {
	return file_wechat_proto_rawDescGZIP(), []int{23}
}

func (x *AutoAuthAesReqData) GetBaseRequest() *BaseRequestPlus {
	if x != nil {
		return x.BaseRequest
	}
	return nil
}

func (x *AutoAuthAesReqData) GetBaseReqInfo() *BaseAuthReqInfo {
	if x != nil {
		return x.BaseReqInfo
	}
	return nil
}

func (x *AutoAuthAesReqData) GetAutoAuthKey() *SKBuiltinString_ {
	if x != nil {
		return x.AutoAuthKey
	}
	return nil
}

func (x *AutoAuthAesReqData) GetImei() string {
	if x != nil && x.Imei != nil {
		return *x.Imei
	}
	return ""
}

func (x *AutoAuthAesReqData) GetSoftType() string {
	if x != nil && x.SoftType != nil {
		return *x.SoftType
	}
	return ""
}

func (x *AutoAuthAesReqData) GetBuiltinIpSeq() uint32 {
	if x != nil && x.BuiltinIpSeq != nil {
		return *x.BuiltinIpSeq
	}
	return 0
}

func (x *AutoAuthAesReqData) GetClientSeqId() string {
	if x != nil && x.ClientSeqId != nil {
		return *x.ClientSeqId
	}
	return ""
}

func (x *AutoAuthAesReqData) GetSignature() string {
	if x != nil && x.Signature != nil {
		return *x.Signature
	}
	return ""
}

func (x *AutoAuthAesReqData) GetDeviceName() string {
	if x != nil && x.DeviceName != nil {
		return *x.DeviceName
	}
	return ""
}

func (x *AutoAuthAesReqData) GetDeviceType() string {
	if x != nil && x.DeviceType != nil {
		return *x.DeviceType
	}
	return ""
}

func (x *AutoAuthAesReqData) GetLanguage() string {
	if x != nil && x.Language != nil {
		return *x.Language
	}
	return ""
}

func (x *AutoAuthAesReqData) GetTimeZone() string {
	if x != nil && x.TimeZone != nil {
		return *x.TimeZone
	}
	return ""
}

func (x *AutoAuthAesReqData) GetChannel() uint32 {
	if x != nil && x.Channel != nil {
		return *x.Channel
	}
	return 0
}

func (x *AutoAuthAesReqData) GetClientCheckData() *SKBuiltinString_ {
	if x != nil {
		return x.ClientCheckData
	}
	return nil
}

func (x *AutoAuthAesReqData) GetExtSpamInfo() *SKBuiltinString_ {
	if x != nil {
		return x.ExtSpamInfo
	}
	return nil
}

type AutoAuthRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RsaReqData *AutoAuthRsaReqData `protobuf:"bytes,1,opt,name=rsa_req_data,json=rsaReqData" json:"rsa_req_data,omitempty"`
	AesReqData *AutoAuthAesReqData `protobuf:"bytes,2,opt,name=aes_req_data,json=aesReqData" json:"aes_req_data,omitempty"`
}

func (x *AutoAuthRequest) Reset() {
	*x = AutoAuthRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wechat_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AutoAuthRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AutoAuthRequest) ProtoMessage() {}

func (x *AutoAuthRequest) ProtoReflect() protoreflect.Message {
	mi := &file_wechat_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AutoAuthRequest.ProtoReflect.Descriptor instead.
func (*AutoAuthRequest) Descriptor() ([]byte, []int) {
	return file_wechat_proto_rawDescGZIP(), []int{24}
}

func (x *AutoAuthRequest) GetRsaReqData() *AutoAuthRsaReqData {
	if x != nil {
		return x.RsaReqData
	}
	return nil
}

func (x *AutoAuthRequest) GetAesReqData() *AutoAuthAesReqData {
	if x != nil {
		return x.AesReqData
	}
	return nil
}

type WCExtInfoNew struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Wcstf          *BufferT `protobuf:"bytes,1,opt,name=Wcstf" json:"Wcstf,omitempty"`
	Wcste          *BufferT `protobuf:"bytes,2,opt,name=Wcste" json:"Wcste,omitempty"`
	CcData         *BufferT `protobuf:"bytes,3,opt,name=CcData" json:"CcData,omitempty"`
	UserAttrInfo   *BufferT `protobuf:"bytes,4,opt,name=UserAttrInfo" json:"UserAttrInfo,omitempty"`
	AcgiDeviceInfo *BufferT `protobuf:"bytes,5,opt,name=AcgiDeviceInfo" json:"AcgiDeviceInfo,omitempty"`
	AcgiTuring     *BufferT `protobuf:"bytes,6,opt,name=AcgiTuring" json:"AcgiTuring,omitempty"`
	DeviceToken    *BufferT `protobuf:"bytes,7,opt,name=DeviceToken" json:"DeviceToken,omitempty"`
	BehaviorId     *BufferT `protobuf:"bytes,8,opt,name=BehaviorId" json:"BehaviorId,omitempty"`
	IosturingHuman *BufferT `protobuf:"bytes,101,opt,name=IosturingHuman" json:"IosturingHuman,omitempty"`
	IosturingOwner *BufferT `protobuf:"bytes,102,opt,name=IosturingOwner" json:"IosturingOwner,omitempty"`
}

func (x *WCExtInfoNew) Reset() {
	*x = WCExtInfoNew{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wechat_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WCExtInfoNew) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WCExtInfoNew) ProtoMessage() {}

func (x *WCExtInfoNew) ProtoReflect() protoreflect.Message {
	mi := &file_wechat_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WCExtInfoNew.ProtoReflect.Descriptor instead.
func (*WCExtInfoNew) Descriptor() ([]byte, []int) {
	return file_wechat_proto_rawDescGZIP(), []int{25}
}

func (x *WCExtInfoNew) GetWcstf() *BufferT {
	if x != nil {
		return x.Wcstf
	}
	return nil
}

func (x *WCExtInfoNew) GetWcste() *BufferT {
	if x != nil {
		return x.Wcste
	}
	return nil
}

func (x *WCExtInfoNew) GetCcData() *BufferT {
	if x != nil {
		return x.CcData
	}
	return nil
}

func (x *WCExtInfoNew) GetUserAttrInfo() *BufferT {
	if x != nil {
		return x.UserAttrInfo
	}
	return nil
}

func (x *WCExtInfoNew) GetAcgiDeviceInfo() *BufferT {
	if x != nil {
		return x.AcgiDeviceInfo
	}
	return nil
}

func (x *WCExtInfoNew) GetAcgiTuring() *BufferT {
	if x != nil {
		return x.AcgiTuring
	}
	return nil
}

func (x *WCExtInfoNew) GetDeviceToken() *BufferT {
	if x != nil {
		return x.DeviceToken
	}
	return nil
}

func (x *WCExtInfoNew) GetBehaviorId() *BufferT {
	if x != nil {
		return x.BehaviorId
	}
	return nil
}

func (x *WCExtInfoNew) GetIosturingHuman() *BufferT {
	if x != nil {
		return x.IosturingHuman
	}
	return nil
}

func (x *WCExtInfoNew) GetIosturingOwner() *BufferT {
	if x != nil {
		return x.IosturingOwner
	}
	return nil
}

type FileInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Filepath *string `protobuf:"bytes,1,opt,name=Filepath" json:"Filepath,omitempty"`
	Fileuuid *string `protobuf:"bytes,2,opt,name=Fileuuid" json:"Fileuuid,omitempty"`
}

func (x *FileInfo) Reset() {
	*x = FileInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wechat_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FileInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FileInfo) ProtoMessage() {}

func (x *FileInfo) ProtoReflect() protoreflect.Message {
	mi := &file_wechat_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FileInfo.ProtoReflect.Descriptor instead.
func (*FileInfo) Descriptor() ([]byte, []int) {
	return file_wechat_proto_rawDescGZIP(), []int{26}
}

func (x *FileInfo) GetFilepath() string {
	if x != nil && x.Filepath != nil {
		return *x.Filepath
	}
	return ""
}

func (x *FileInfo) GetFileuuid() string {
	if x != nil && x.Fileuuid != nil {
		return *x.Fileuuid
	}
	return ""
}

type SpamDataBody struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UnKnown1              *int32      `protobuf:"varint,1,req,name=UnKnown1" json:"UnKnown1,omitempty"`
	TimeStamp             *uint32     `protobuf:"varint,2,req,name=TimeStamp" json:"TimeStamp,omitempty"`
	KeyHash               *int32      `protobuf:"varint,3,req,name=KeyHash" json:"KeyHash,omitempty"`
	Yes1                  *string     `protobuf:"bytes,11,req,name=Yes1" json:"Yes1,omitempty"`
	Yes2                  *string     `protobuf:"bytes,12,req,name=Yes2" json:"Yes2,omitempty"`
	IosVersion            *string     `protobuf:"bytes,13,req,name=IosVersion" json:"IosVersion,omitempty"`
	DeviceType            *string     `protobuf:"bytes,14,req,name=DeviceType" json:"DeviceType,omitempty"`
	UnKnown2              *int32      `protobuf:"varint,15,req,name=UnKnown2" json:"UnKnown2,omitempty"`
	IdentifierForVendor   *string     `protobuf:"bytes,16,req,name=IdentifierForVendor" json:"IdentifierForVendor,omitempty"`
	AdvertisingIdentifier *string     `protobuf:"bytes,17,req,name=AdvertisingIdentifier" json:"AdvertisingIdentifier,omitempty"`
	Carrier               *string     `protobuf:"bytes,18,req,name=Carrier" json:"Carrier,omitempty"`
	BatteryInfo           *int32      `protobuf:"varint,19,req,name=BatteryInfo" json:"BatteryInfo,omitempty"`
	NetworkName           *string     `protobuf:"bytes,20,req,name=NetworkName" json:"NetworkName,omitempty"`
	NetType               *int32      `protobuf:"varint,21,req,name=NetType" json:"NetType,omitempty"`
	AppBundleId           *string     `protobuf:"bytes,22,req,name=AppBundleId" json:"AppBundleId,omitempty"`
	DeviceName            *string     `protobuf:"bytes,23,req,name=DeviceName" json:"DeviceName,omitempty"`
	UserName              *string     `protobuf:"bytes,24,req,name=UserName" json:"UserName,omitempty"`
	Unknown3              *int64      `protobuf:"varint,25,req,name=Unknown3" json:"Unknown3,omitempty"`
	Unknown4              *int64      `protobuf:"varint,26,req,name=Unknown4" json:"Unknown4,omitempty"`
	Unknown5              *int32      `protobuf:"varint,27,req,name=Unknown5" json:"Unknown5,omitempty"`
	Unknown6              *int32      `protobuf:"varint,28,req,name=Unknown6" json:"Unknown6,omitempty"`
	Lang                  *string     `protobuf:"bytes,29,req,name=Lang" json:"Lang,omitempty"`
	Country               *string     `protobuf:"bytes,30,req,name=Country" json:"Country,omitempty"`
	Unknown7              *int32      `protobuf:"varint,31,req,name=Unknown7" json:"Unknown7,omitempty"`
	DocumentDir           *string     `protobuf:"bytes,32,req,name=DocumentDir" json:"DocumentDir,omitempty"`
	Unknown8              *int32      `protobuf:"varint,33,req,name=Unknown8" json:"Unknown8,omitempty"`
	Unknown9              *int32      `protobuf:"varint,34,req,name=Unknown9" json:"Unknown9,omitempty"`
	HeadMD5               *string     `protobuf:"bytes,35,req,name=HeadMD5" json:"HeadMD5,omitempty"`
	AppUUID               *string     `protobuf:"bytes,36,req,name=AppUUID" json:"AppUUID,omitempty"`
	SyslogUUID            *string     `protobuf:"bytes,37,req,name=SyslogUUID" json:"SyslogUUID,omitempty"`
	WifiName              *string     `protobuf:"bytes,38,req,name=WifiName" json:"WifiName,omitempty"`
	WifiMac               *string     `protobuf:"bytes,39,req,name=WifiMac" json:"WifiMac,omitempty"`
	AppName               *string     `protobuf:"bytes,40,req,name=AppName" json:"AppName,omitempty"`
	SshPath               *string     `protobuf:"bytes,41,opt,name=SshPath" json:"SshPath,omitempty"`
	TempTest              *string     `protobuf:"bytes,42,opt,name=TempTest" json:"TempTest,omitempty"`
	DevMD5                *string     `protobuf:"bytes,43,opt,name=DevMD5" json:"DevMD5,omitempty"`
	DevUser               *string     `protobuf:"bytes,44,opt,name=DevUser" json:"DevUser,omitempty"`
	DevPrefix             *string     `protobuf:"bytes,45,opt,name=DevPrefix" json:"DevPrefix,omitempty"`
	AppFileInfo           []*FileInfo `protobuf:"bytes,46,rep,name=AppFileInfo" json:"AppFileInfo,omitempty"`
	Unknown12             *string     `protobuf:"bytes,47,req,name=Unknown12" json:"Unknown12,omitempty"`
	IsModify              *int32      `protobuf:"varint,50,req,name=IsModify" json:"IsModify,omitempty"`
	ModifyMD5             *string     `protobuf:"bytes,51,req,name=ModifyMD5" json:"ModifyMD5,omitempty"`
	RqtHash               *int64      `protobuf:"varint,52,req,name=RqtHash" json:"RqtHash,omitempty"`
	Unknown13             *int32      `protobuf:"varint,53,req,name=Unknown13" json:"Unknown13,omitempty"`
	Unknown14             *int32      `protobuf:"varint,54,req,name=Unknown14" json:"Unknown14,omitempty"`
	Ssid                  *string     `protobuf:"bytes,55,req,name=Ssid" json:"Ssid,omitempty"`
	Unknown15             *int32      `protobuf:"varint,56,req,name=Unknown15" json:"Unknown15,omitempty"`
	Bssid                 *string     `protobuf:"bytes,57,req,name=Bssid" json:"Bssid,omitempty"`
	IsJail                *int32      `protobuf:"varint,58,req,name=IsJail" json:"IsJail,omitempty"`
	Seid                  *string     `protobuf:"bytes,59,opt,name=Seid" json:"Seid,omitempty"`
	Unknown16             *int32      `protobuf:"varint,60,opt,name=Unknown16" json:"Unknown16,omitempty"`
	Unknown17             *int32      `protobuf:"varint,61,opt,name=Unknown17" json:"Unknown17,omitempty"`
	Unknown18             *int32      `protobuf:"varint,62,opt,name=Unknown18" json:"Unknown18,omitempty"`
	WifiOn                *int32      `protobuf:"varint,63,opt,name=WifiOn" json:"WifiOn,omitempty"`
	BluethOn              *int32      `protobuf:"varint,64,opt,name=BluethOn" json:"BluethOn,omitempty"`
	BluethName            *string     `protobuf:"bytes,65,opt,name=BluethName" json:"BluethName,omitempty"`
	BluethMac             *string     `protobuf:"bytes,66,opt,name=BluethMac" json:"BluethMac,omitempty"`
	Unknown19             *int32      `protobuf:"varint,67,opt,name=Unknown19" json:"Unknown19,omitempty"`
	Unknown20             *int32      `protobuf:"varint,68,opt,name=Unknown20" json:"Unknown20,omitempty"`
	Unknown26             *int32      `protobuf:"varint,69,opt,name=Unknown26" json:"Unknown26,omitempty"`
	HasSim                *int32      `protobuf:"varint,70,opt,name=HasSim" json:"HasSim,omitempty"`
	UsbState              *int32      `protobuf:"varint,71,opt,name=UsbState" json:"UsbState,omitempty"`
	Unknown27             *int32      `protobuf:"varint,72,opt,name=Unknown27" json:"Unknown27,omitempty"`
	Unknown28             *int32      `protobuf:"varint,73,opt,name=Unknown28" json:"Unknown28,omitempty"`
	Sign                  *string     `protobuf:"bytes,74,opt,name=Sign" json:"Sign,omitempty"`
	PackageFlag           *uint32     `protobuf:"varint,75,opt,name=PackageFlag" json:"PackageFlag,omitempty"`
	AccessFlag            *uint32     `protobuf:"varint,76,opt,name=AccessFlag" json:"AccessFlag,omitempty"`
	Imei                  *string     `protobuf:"bytes,77,opt,name=Imei" json:"Imei,omitempty"`
	DevSerial             *string     `protobuf:"bytes,78,opt,name=DevSerial" json:"DevSerial,omitempty"`
	Unknown29             *uint32     `protobuf:"varint,79,opt,name=Unknown29" json:"Unknown29,omitempty"`
	Unknown30             *uint32     `protobuf:"varint,80,opt,name=Unknown30" json:"Unknown30,omitempty"`
	Unknown31             *uint32     `protobuf:"varint,81,opt,name=Unknown31" json:"Unknown31,omitempty"`
	Unknown32             *uint32     `protobuf:"varint,82,opt,name=Unknown32" json:"Unknown32,omitempty"`
	AppNum                *uint32     `protobuf:"varint,83,opt,name=AppNum" json:"AppNum,omitempty"`
	Totcapacity           *string     `protobuf:"bytes,84,opt,name=Totcapacity" json:"Totcapacity,omitempty"`
	Avacapacity           *string     `protobuf:"bytes,85,opt,name=Avacapacity" json:"Avacapacity,omitempty"`
	Unknown33             *uint32     `protobuf:"varint,86,opt,name=Unknown33" json:"Unknown33,omitempty"`
	Unknown34             *uint32     `protobuf:"varint,87,opt,name=Unknown34" json:"Unknown34,omitempty"`
	Unknown35             *uint32     `protobuf:"varint,88,opt,name=Unknown35" json:"Unknown35,omitempty"`
	Unknown103            *int32      `protobuf:"varint,89,opt,name=Unknown103" json:"Unknown103,omitempty"`
	Unknown104            *int32      `protobuf:"varint,90,opt,name=Unknown104" json:"Unknown104,omitempty"`
	Unknown105            *int32      `protobuf:"varint,91,opt,name=Unknown105" json:"Unknown105,omitempty"`
	Unknown106            *uint32     `protobuf:"varint,92,opt,name=Unknown106" json:"Unknown106,omitempty"`
	Unknown107            *int32      `protobuf:"varint,93,opt,name=Unknown107" json:"Unknown107,omitempty"`
	Unknown108            *int32      `protobuf:"varint,94,opt,name=Unknown108" json:"Unknown108,omitempty"`
	Unknown109            *int32      `protobuf:"varint,95,opt,name=Unknown109" json:"Unknown109,omitempty"`
	Unknown110            *int32      `protobuf:"varint,96,opt,name=Unknown110" json:"Unknown110,omitempty"`
	Unknown111            *int32      `protobuf:"varint,97,opt,name=Unknown111" json:"Unknown111,omitempty"`
	Unknown112            *uint32     `protobuf:"varint,98,opt,name=Unknown112" json:"Unknown112,omitempty"`
}

func (x *SpamDataBody) Reset() {
	*x = SpamDataBody{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wechat_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SpamDataBody) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SpamDataBody) ProtoMessage() {}

func (x *SpamDataBody) ProtoReflect() protoreflect.Message {
	mi := &file_wechat_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SpamDataBody.ProtoReflect.Descriptor instead.
func (*SpamDataBody) Descriptor() ([]byte, []int) {
	return file_wechat_proto_rawDescGZIP(), []int{27}
}

func (x *SpamDataBody) GetUnKnown1() int32 {
	if x != nil && x.UnKnown1 != nil {
		return *x.UnKnown1
	}
	return 0
}

func (x *SpamDataBody) GetTimeStamp() uint32 {
	if x != nil && x.TimeStamp != nil {
		return *x.TimeStamp
	}
	return 0
}

func (x *SpamDataBody) GetKeyHash() int32 {
	if x != nil && x.KeyHash != nil {
		return *x.KeyHash
	}
	return 0
}

func (x *SpamDataBody) GetYes1() string {
	if x != nil && x.Yes1 != nil {
		return *x.Yes1
	}
	return ""
}

func (x *SpamDataBody) GetYes2() string {
	if x != nil && x.Yes2 != nil {
		return *x.Yes2
	}
	return ""
}

func (x *SpamDataBody) GetIosVersion() string {
	if x != nil && x.IosVersion != nil {
		return *x.IosVersion
	}
	return ""
}

func (x *SpamDataBody) GetDeviceType() string {
	if x != nil && x.DeviceType != nil {
		return *x.DeviceType
	}
	return ""
}

func (x *SpamDataBody) GetUnKnown2() int32 {
	if x != nil && x.UnKnown2 != nil {
		return *x.UnKnown2
	}
	return 0
}

func (x *SpamDataBody) GetIdentifierForVendor() string {
	if x != nil && x.IdentifierForVendor != nil {
		return *x.IdentifierForVendor
	}
	return ""
}

func (x *SpamDataBody) GetAdvertisingIdentifier() string {
	if x != nil && x.AdvertisingIdentifier != nil {
		return *x.AdvertisingIdentifier
	}
	return ""
}

func (x *SpamDataBody) GetCarrier() string {
	if x != nil && x.Carrier != nil {
		return *x.Carrier
	}
	return ""
}

func (x *SpamDataBody) GetBatteryInfo() int32 {
	if x != nil && x.BatteryInfo != nil {
		return *x.BatteryInfo
	}
	return 0
}

func (x *SpamDataBody) GetNetworkName() string {
	if x != nil && x.NetworkName != nil {
		return *x.NetworkName
	}
	return ""
}

func (x *SpamDataBody) GetNetType() int32 {
	if x != nil && x.NetType != nil {
		return *x.NetType
	}
	return 0
}

func (x *SpamDataBody) GetAppBundleId() string {
	if x != nil && x.AppBundleId != nil {
		return *x.AppBundleId
	}
	return ""
}

func (x *SpamDataBody) GetDeviceName() string {
	if x != nil && x.DeviceName != nil {
		return *x.DeviceName
	}
	return ""
}

func (x *SpamDataBody) GetUserName() string {
	if x != nil && x.UserName != nil {
		return *x.UserName
	}
	return ""
}

func (x *SpamDataBody) GetUnknown3() int64 {
	if x != nil && x.Unknown3 != nil {
		return *x.Unknown3
	}
	return 0
}

func (x *SpamDataBody) GetUnknown4() int64 {
	if x != nil && x.Unknown4 != nil {
		return *x.Unknown4
	}
	return 0
}

func (x *SpamDataBody) GetUnknown5() int32 {
	if x != nil && x.Unknown5 != nil {
		return *x.Unknown5
	}
	return 0
}

func (x *SpamDataBody) GetUnknown6() int32 {
	if x != nil && x.Unknown6 != nil {
		return *x.Unknown6
	}
	return 0
}

func (x *SpamDataBody) GetLang() string {
	if x != nil && x.Lang != nil {
		return *x.Lang
	}
	return ""
}

func (x *SpamDataBody) GetCountry() string {
	if x != nil && x.Country != nil {
		return *x.Country
	}
	return ""
}

func (x *SpamDataBody) GetUnknown7() int32 {
	if x != nil && x.Unknown7 != nil {
		return *x.Unknown7
	}
	return 0
}

func (x *SpamDataBody) GetDocumentDir() string {
	if x != nil && x.DocumentDir != nil {
		return *x.DocumentDir
	}
	return ""
}

func (x *SpamDataBody) GetUnknown8() int32 {
	if x != nil && x.Unknown8 != nil {
		return *x.Unknown8
	}
	return 0
}

func (x *SpamDataBody) GetUnknown9() int32 {
	if x != nil && x.Unknown9 != nil {
		return *x.Unknown9
	}
	return 0
}

func (x *SpamDataBody) GetHeadMD5() string {
	if x != nil && x.HeadMD5 != nil {
		return *x.HeadMD5
	}
	return ""
}

func (x *SpamDataBody) GetAppUUID() string {
	if x != nil && x.AppUUID != nil {
		return *x.AppUUID
	}
	return ""
}

func (x *SpamDataBody) GetSyslogUUID() string {
	if x != nil && x.SyslogUUID != nil {
		return *x.SyslogUUID
	}
	return ""
}

func (x *SpamDataBody) GetWifiName() string {
	if x != nil && x.WifiName != nil {
		return *x.WifiName
	}
	return ""
}

func (x *SpamDataBody) GetWifiMac() string {
	if x != nil && x.WifiMac != nil {
		return *x.WifiMac
	}
	return ""
}

func (x *SpamDataBody) GetAppName() string {
	if x != nil && x.AppName != nil {
		return *x.AppName
	}
	return ""
}

func (x *SpamDataBody) GetSshPath() string {
	if x != nil && x.SshPath != nil {
		return *x.SshPath
	}
	return ""
}

func (x *SpamDataBody) GetTempTest() string {
	if x != nil && x.TempTest != nil {
		return *x.TempTest
	}
	return ""
}

func (x *SpamDataBody) GetDevMD5() string {
	if x != nil && x.DevMD5 != nil {
		return *x.DevMD5
	}
	return ""
}

func (x *SpamDataBody) GetDevUser() string {
	if x != nil && x.DevUser != nil {
		return *x.DevUser
	}
	return ""
}

func (x *SpamDataBody) GetDevPrefix() string {
	if x != nil && x.DevPrefix != nil {
		return *x.DevPrefix
	}
	return ""
}

func (x *SpamDataBody) GetAppFileInfo() []*FileInfo {
	if x != nil {
		return x.AppFileInfo
	}
	return nil
}

func (x *SpamDataBody) GetUnknown12() string {
	if x != nil && x.Unknown12 != nil {
		return *x.Unknown12
	}
	return ""
}

func (x *SpamDataBody) GetIsModify() int32 {
	if x != nil && x.IsModify != nil {
		return *x.IsModify
	}
	return 0
}

func (x *SpamDataBody) GetModifyMD5() string {
	if x != nil && x.ModifyMD5 != nil {
		return *x.ModifyMD5
	}
	return ""
}

func (x *SpamDataBody) GetRqtHash() int64 {
	if x != nil && x.RqtHash != nil {
		return *x.RqtHash
	}
	return 0
}

func (x *SpamDataBody) GetUnknown13() int32 {
	if x != nil && x.Unknown13 != nil {
		return *x.Unknown13
	}
	return 0
}

func (x *SpamDataBody) GetUnknown14() int32 {
	if x != nil && x.Unknown14 != nil {
		return *x.Unknown14
	}
	return 0
}

func (x *SpamDataBody) GetSsid() string {
	if x != nil && x.Ssid != nil {
		return *x.Ssid
	}
	return ""
}

func (x *SpamDataBody) GetUnknown15() int32 {
	if x != nil && x.Unknown15 != nil {
		return *x.Unknown15
	}
	return 0
}

func (x *SpamDataBody) GetBssid() string {
	if x != nil && x.Bssid != nil {
		return *x.Bssid
	}
	return ""
}

func (x *SpamDataBody) GetIsJail() int32 {
	if x != nil && x.IsJail != nil {
		return *x.IsJail
	}
	return 0
}

func (x *SpamDataBody) GetSeid() string {
	if x != nil && x.Seid != nil {
		return *x.Seid
	}
	return ""
}

func (x *SpamDataBody) GetUnknown16() int32 {
	if x != nil && x.Unknown16 != nil {
		return *x.Unknown16
	}
	return 0
}

func (x *SpamDataBody) GetUnknown17() int32 {
	if x != nil && x.Unknown17 != nil {
		return *x.Unknown17
	}
	return 0
}

func (x *SpamDataBody) GetUnknown18() int32 {
	if x != nil && x.Unknown18 != nil {
		return *x.Unknown18
	}
	return 0
}

func (x *SpamDataBody) GetWifiOn() int32 {
	if x != nil && x.WifiOn != nil {
		return *x.WifiOn
	}
	return 0
}

func (x *SpamDataBody) GetBluethOn() int32 {
	if x != nil && x.BluethOn != nil {
		return *x.BluethOn
	}
	return 0
}

func (x *SpamDataBody) GetBluethName() string {
	if x != nil && x.BluethName != nil {
		return *x.BluethName
	}
	return ""
}

func (x *SpamDataBody) GetBluethMac() string {
	if x != nil && x.BluethMac != nil {
		return *x.BluethMac
	}
	return ""
}

func (x *SpamDataBody) GetUnknown19() int32 {
	if x != nil && x.Unknown19 != nil {
		return *x.Unknown19
	}
	return 0
}

func (x *SpamDataBody) GetUnknown20() int32 {
	if x != nil && x.Unknown20 != nil {
		return *x.Unknown20
	}
	return 0
}

func (x *SpamDataBody) GetUnknown26() int32 {
	if x != nil && x.Unknown26 != nil {
		return *x.Unknown26
	}
	return 0
}

func (x *SpamDataBody) GetHasSim() int32 {
	if x != nil && x.HasSim != nil {
		return *x.HasSim
	}
	return 0
}

func (x *SpamDataBody) GetUsbState() int32 {
	if x != nil && x.UsbState != nil {
		return *x.UsbState
	}
	return 0
}

func (x *SpamDataBody) GetUnknown27() int32 {
	if x != nil && x.Unknown27 != nil {
		return *x.Unknown27
	}
	return 0
}

func (x *SpamDataBody) GetUnknown28() int32 {
	if x != nil && x.Unknown28 != nil {
		return *x.Unknown28
	}
	return 0
}

func (x *SpamDataBody) GetSign() string {
	if x != nil && x.Sign != nil {
		return *x.Sign
	}
	return ""
}

func (x *SpamDataBody) GetPackageFlag() uint32 {
	if x != nil && x.PackageFlag != nil {
		return *x.PackageFlag
	}
	return 0
}

func (x *SpamDataBody) GetAccessFlag() uint32 {
	if x != nil && x.AccessFlag != nil {
		return *x.AccessFlag
	}
	return 0
}

func (x *SpamDataBody) GetImei() string {
	if x != nil && x.Imei != nil {
		return *x.Imei
	}
	return ""
}

func (x *SpamDataBody) GetDevSerial() string {
	if x != nil && x.DevSerial != nil {
		return *x.DevSerial
	}
	return ""
}

func (x *SpamDataBody) GetUnknown29() uint32 {
	if x != nil && x.Unknown29 != nil {
		return *x.Unknown29
	}
	return 0
}

func (x *SpamDataBody) GetUnknown30() uint32 {
	if x != nil && x.Unknown30 != nil {
		return *x.Unknown30
	}
	return 0
}

func (x *SpamDataBody) GetUnknown31() uint32 {
	if x != nil && x.Unknown31 != nil {
		return *x.Unknown31
	}
	return 0
}

func (x *SpamDataBody) GetUnknown32() uint32 {
	if x != nil && x.Unknown32 != nil {
		return *x.Unknown32
	}
	return 0
}

func (x *SpamDataBody) GetAppNum() uint32 {
	if x != nil && x.AppNum != nil {
		return *x.AppNum
	}
	return 0
}

func (x *SpamDataBody) GetTotcapacity() string {
	if x != nil && x.Totcapacity != nil {
		return *x.Totcapacity
	}
	return ""
}

func (x *SpamDataBody) GetAvacapacity() string {
	if x != nil && x.Avacapacity != nil {
		return *x.Avacapacity
	}
	return ""
}

func (x *SpamDataBody) GetUnknown33() uint32 {
	if x != nil && x.Unknown33 != nil {
		return *x.Unknown33
	}
	return 0
}

func (x *SpamDataBody) GetUnknown34() uint32 {
	if x != nil && x.Unknown34 != nil {
		return *x.Unknown34
	}
	return 0
}

func (x *SpamDataBody) GetUnknown35() uint32 {
	if x != nil && x.Unknown35 != nil {
		return *x.Unknown35
	}
	return 0
}

func (x *SpamDataBody) GetUnknown103() int32 {
	if x != nil && x.Unknown103 != nil {
		return *x.Unknown103
	}
	return 0
}

func (x *SpamDataBody) GetUnknown104() int32 {
	if x != nil && x.Unknown104 != nil {
		return *x.Unknown104
	}
	return 0
}

func (x *SpamDataBody) GetUnknown105() int32 {
	if x != nil && x.Unknown105 != nil {
		return *x.Unknown105
	}
	return 0
}

func (x *SpamDataBody) GetUnknown106() uint32 {
	if x != nil && x.Unknown106 != nil {
		return *x.Unknown106
	}
	return 0
}

func (x *SpamDataBody) GetUnknown107() int32 {
	if x != nil && x.Unknown107 != nil {
		return *x.Unknown107
	}
	return 0
}

func (x *SpamDataBody) GetUnknown108() int32 {
	if x != nil && x.Unknown108 != nil {
		return *x.Unknown108
	}
	return 0
}

func (x *SpamDataBody) GetUnknown109() int32 {
	if x != nil && x.Unknown109 != nil {
		return *x.Unknown109
	}
	return 0
}

func (x *SpamDataBody) GetUnknown110() int32 {
	if x != nil && x.Unknown110 != nil {
		return *x.Unknown110
	}
	return 0
}

func (x *SpamDataBody) GetUnknown111() int32 {
	if x != nil && x.Unknown111 != nil {
		return *x.Unknown111
	}
	return 0
}

func (x *SpamDataBody) GetUnknown112() uint32 {
	if x != nil && x.Unknown112 != nil {
		return *x.Unknown112
	}
	return 0
}

type NewClientCheckData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	C32CData  *int64 `protobuf:"varint,1,opt,name=C32cData" json:"C32cData,omitempty"`
	TimeStamp *int64 `protobuf:"varint,2,opt,name=TimeStamp" json:"TimeStamp,omitempty"`
	DataBody  []byte `protobuf:"bytes,3,opt,name=DataBody" json:"DataBody,omitempty"`
}

func (x *NewClientCheckData) Reset() {
	*x = NewClientCheckData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wechat_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NewClientCheckData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NewClientCheckData) ProtoMessage() {}

func (x *NewClientCheckData) ProtoReflect() protoreflect.Message {
	mi := &file_wechat_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NewClientCheckData.ProtoReflect.Descriptor instead.
func (*NewClientCheckData) Descriptor() ([]byte, []int) {
	return file_wechat_proto_rawDescGZIP(), []int{28}
}

func (x *NewClientCheckData) GetC32CData() int64 {
	if x != nil && x.C32CData != nil {
		return *x.C32CData
	}
	return 0
}

func (x *NewClientCheckData) GetTimeStamp() int64 {
	if x != nil && x.TimeStamp != nil {
		return *x.TimeStamp
	}
	return 0
}

func (x *NewClientCheckData) GetDataBody() []byte {
	if x != nil {
		return x.DataBody
	}
	return nil
}

type DeviceRunningInfoNew struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Version     []byte  `protobuf:"bytes,1,req,name=Version" json:"Version,omitempty"`
	Type        *uint32 `protobuf:"varint,2,req,name=Type" json:"Type,omitempty"`
	EncryptData []byte  `protobuf:"bytes,3,req,name=EncryptData" json:"EncryptData,omitempty"`
	Timestamp   *uint32 `protobuf:"varint,4,req,name=Timestamp" json:"Timestamp,omitempty"`
	Unknown5    *uint32 `protobuf:"varint,5,req,name=Unknown5" json:"Unknown5,omitempty"`
	Unknown6    *uint32 `protobuf:"varint,6,req,name=Unknown6" json:"Unknown6,omitempty"`
}

func (x *DeviceRunningInfoNew) Reset() {
	*x = DeviceRunningInfoNew{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wechat_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeviceRunningInfoNew) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceRunningInfoNew) ProtoMessage() {}

func (x *DeviceRunningInfoNew) ProtoReflect() protoreflect.Message {
	mi := &file_wechat_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceRunningInfoNew.ProtoReflect.Descriptor instead.
func (*DeviceRunningInfoNew) Descriptor() ([]byte, []int) {
	return file_wechat_proto_rawDescGZIP(), []int{29}
}

func (x *DeviceRunningInfoNew) GetVersion() []byte {
	if x != nil {
		return x.Version
	}
	return nil
}

func (x *DeviceRunningInfoNew) GetType() uint32 {
	if x != nil && x.Type != nil {
		return *x.Type
	}
	return 0
}

func (x *DeviceRunningInfoNew) GetEncryptData() []byte {
	if x != nil {
		return x.EncryptData
	}
	return nil
}

func (x *DeviceRunningInfoNew) GetTimestamp() uint32 {
	if x != nil && x.Timestamp != nil {
		return *x.Timestamp
	}
	return 0
}

func (x *DeviceRunningInfoNew) GetUnknown5() uint32 {
	if x != nil && x.Unknown5 != nil {
		return *x.Unknown5
	}
	return 0
}

func (x *DeviceRunningInfoNew) GetUnknown6() uint32 {
	if x != nil && x.Unknown6 != nil {
		return *x.Unknown6
	}
	return 0
}

type WCSTF struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StartTime *uint64  `protobuf:"varint,1,req,name=StartTime" json:"StartTime,omitempty"`
	CheckTime *uint64  `protobuf:"varint,2,req,name=CheckTime" json:"CheckTime,omitempty"`
	Count     *uint32  `protobuf:"varint,3,req,name=Count" json:"Count,omitempty"`
	EndTime   []uint64 `protobuf:"varint,4,rep,name=EndTime" json:"EndTime,omitempty"`
}

func (x *WCSTF) Reset() {
	*x = WCSTF{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wechat_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WCSTF) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WCSTF) ProtoMessage() {}

func (x *WCSTF) ProtoReflect() protoreflect.Message {
	mi := &file_wechat_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WCSTF.ProtoReflect.Descriptor instead.
func (*WCSTF) Descriptor() ([]byte, []int) {
	return file_wechat_proto_rawDescGZIP(), []int{30}
}

func (x *WCSTF) GetStartTime() uint64 {
	if x != nil && x.StartTime != nil {
		return *x.StartTime
	}
	return 0
}

func (x *WCSTF) GetCheckTime() uint64 {
	if x != nil && x.CheckTime != nil {
		return *x.CheckTime
	}
	return 0
}

func (x *WCSTF) GetCount() uint32 {
	if x != nil && x.Count != nil {
		return *x.Count
	}
	return 0
}

func (x *WCSTF) GetEndTime() []uint64 {
	if x != nil {
		return x.EndTime
	}
	return nil
}

type WCSTE struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CheckId   *string `protobuf:"bytes,1,req,name=CheckId" json:"CheckId,omitempty"`
	StartTime *uint32 `protobuf:"varint,2,req,name=StartTime" json:"StartTime,omitempty"`
	CheckTime *uint32 `protobuf:"varint,3,req,name=CheckTime" json:"CheckTime,omitempty"`
	Count1    *uint32 `protobuf:"varint,4,req,name=Count1" json:"Count1,omitempty"`
	Count2    *uint32 `protobuf:"varint,5,req,name=Count2" json:"Count2,omitempty"`
	Count3    *uint32 `protobuf:"varint,6,req,name=Count3" json:"Count3,omitempty"`
	Const1    *uint64 `protobuf:"varint,7,req,name=Const1" json:"Const1,omitempty"`
	Const2    *uint64 `protobuf:"varint,8,req,name=Const2" json:"Const2,omitempty"`
	Const3    *uint64 `protobuf:"varint,9,req,name=Const3" json:"Const3,omitempty"`
	Const4    *uint64 `protobuf:"varint,10,req,name=Const4" json:"Const4,omitempty"`
	Const5    *uint64 `protobuf:"varint,11,req,name=Const5" json:"Const5,omitempty"`
	Const6    *uint64 `protobuf:"varint,12,req,name=Const6" json:"Const6,omitempty"`
}

func (x *WCSTE) Reset() {
	*x = WCSTE{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wechat_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WCSTE) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WCSTE) ProtoMessage() {}

func (x *WCSTE) ProtoReflect() protoreflect.Message {
	mi := &file_wechat_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WCSTE.ProtoReflect.Descriptor instead.
func (*WCSTE) Descriptor() ([]byte, []int) {
	return file_wechat_proto_rawDescGZIP(), []int{31}
}

func (x *WCSTE) GetCheckId() string {
	if x != nil && x.CheckId != nil {
		return *x.CheckId
	}
	return ""
}

func (x *WCSTE) GetStartTime() uint32 {
	if x != nil && x.StartTime != nil {
		return *x.StartTime
	}
	return 0
}

func (x *WCSTE) GetCheckTime() uint32 {
	if x != nil && x.CheckTime != nil {
		return *x.CheckTime
	}
	return 0
}

func (x *WCSTE) GetCount1() uint32 {
	if x != nil && x.Count1 != nil {
		return *x.Count1
	}
	return 0
}

func (x *WCSTE) GetCount2() uint32 {
	if x != nil && x.Count2 != nil {
		return *x.Count2
	}
	return 0
}

func (x *WCSTE) GetCount3() uint32 {
	if x != nil && x.Count3 != nil {
		return *x.Count3
	}
	return 0
}

func (x *WCSTE) GetConst1() uint64 {
	if x != nil && x.Const1 != nil {
		return *x.Const1
	}
	return 0
}

func (x *WCSTE) GetConst2() uint64 {
	if x != nil && x.Const2 != nil {
		return *x.Const2
	}
	return 0
}

func (x *WCSTE) GetConst3() uint64 {
	if x != nil && x.Const3 != nil {
		return *x.Const3
	}
	return 0
}

func (x *WCSTE) GetConst4() uint64 {
	if x != nil && x.Const4 != nil {
		return *x.Const4
	}
	return 0
}

func (x *WCSTE) GetConst5() uint64 {
	if x != nil && x.Const5 != nil {
		return *x.Const5
	}
	return 0
}

func (x *WCSTE) GetConst6() uint64 {
	if x != nil && x.Const6 != nil {
		return *x.Const6
	}
	return 0
}

type SaeInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type          []byte  `protobuf:"bytes,1,opt,name=type" json:"type,omitempty"`
	Iv            []byte  `protobuf:"bytes,2,opt,name=iv" json:"iv,omitempty"`
	Len           *uint32 `protobuf:"varint,3,opt,name=len" json:"len,omitempty"`
	UnknowValue9  []byte  `protobuf:"bytes,9,opt,name=unknowValue9" json:"unknowValue9,omitempty"`
	TableKey      []byte  `protobuf:"bytes,10,opt,name=tableKey" json:"tableKey,omitempty"`
	UnknowValue11 []byte  `protobuf:"bytes,11,opt,name=unknowValue11" json:"unknowValue11,omitempty"`
	TableValue    []byte  `protobuf:"bytes,12,opt,name=tableValue" json:"tableValue,omitempty"`
	UnknowValue18 []byte  `protobuf:"bytes,18,opt,name=unknowValue18" json:"unknowValue18,omitempty"`
}

func (x *SaeInfo) Reset() {
	*x = SaeInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wechat_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SaeInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaeInfo) ProtoMessage() {}

func (x *SaeInfo) ProtoReflect() protoreflect.Message {
	mi := &file_wechat_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaeInfo.ProtoReflect.Descriptor instead.
func (*SaeInfo) Descriptor() ([]byte, []int) {
	return file_wechat_proto_rawDescGZIP(), []int{32}
}

func (x *SaeInfo) GetType() []byte {
	if x != nil {
		return x.Type
	}
	return nil
}

func (x *SaeInfo) GetIv() []byte {
	if x != nil {
		return x.Iv
	}
	return nil
}

func (x *SaeInfo) GetLen() uint32 {
	if x != nil && x.Len != nil {
		return *x.Len
	}
	return 0
}

func (x *SaeInfo) GetUnknowValue9() []byte {
	if x != nil {
		return x.UnknowValue9
	}
	return nil
}

func (x *SaeInfo) GetTableKey() []byte {
	if x != nil {
		return x.TableKey
	}
	return nil
}

func (x *SaeInfo) GetUnknowValue11() []byte {
	if x != nil {
		return x.UnknowValue11
	}
	return nil
}

func (x *SaeInfo) GetTableValue() []byte {
	if x != nil {
		return x.TableValue
	}
	return nil
}

func (x *SaeInfo) GetUnknowValue18() []byte {
	if x != nil {
		return x.UnknowValue18
	}
	return nil
}

type TenPayRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BaseRequest *BaseRequest      `protobuf:"bytes,1,opt,name=baseRequest" json:"baseRequest,omitempty"`
	CgiCmd      *uint32           `protobuf:"varint,2,opt,name=cgiCmd" json:"cgiCmd,omitempty"`
	OutPutType  *uint32           `protobuf:"varint,3,opt,name=outPutType" json:"outPutType,omitempty"`
	ReqText     *SKBuiltinString_ `protobuf:"bytes,4,opt,name=reqText" json:"reqText,omitempty"`
	ReqTextWx   *SKBuiltinString_ `protobuf:"bytes,5,opt,name=reqTextWx" json:"reqTextWx,omitempty"`
	Sign        *string           `protobuf:"bytes,6,opt,name=sign" json:"sign,omitempty"`
	CrtNo       *string           `protobuf:"bytes,7,opt,name=crtNo" json:"crtNo,omitempty"`
}

func (x *TenPayRequest) Reset() {
	*x = TenPayRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wechat_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TenPayRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TenPayRequest) ProtoMessage() {}

func (x *TenPayRequest) ProtoReflect() protoreflect.Message {
	mi := &file_wechat_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TenPayRequest.ProtoReflect.Descriptor instead.
func (*TenPayRequest) Descriptor() ([]byte, []int) {
	return file_wechat_proto_rawDescGZIP(), []int{33}
}

func (x *TenPayRequest) GetBaseRequest() *BaseRequest {
	if x != nil {
		return x.BaseRequest
	}
	return nil
}

func (x *TenPayRequest) GetCgiCmd() uint32 {
	if x != nil && x.CgiCmd != nil {
		return *x.CgiCmd
	}
	return 0
}

func (x *TenPayRequest) GetOutPutType() uint32 {
	if x != nil && x.OutPutType != nil {
		return *x.OutPutType
	}
	return 0
}

func (x *TenPayRequest) GetReqText() *SKBuiltinString_ {
	if x != nil {
		return x.ReqText
	}
	return nil
}

func (x *TenPayRequest) GetReqTextWx() *SKBuiltinString_ {
	if x != nil {
		return x.ReqTextWx
	}
	return nil
}

func (x *TenPayRequest) GetSign() string {
	if x != nil && x.Sign != nil {
		return *x.Sign
	}
	return ""
}

func (x *TenPayRequest) GetCrtNo() string {
	if x != nil && x.CrtNo != nil {
		return *x.CrtNo
	}
	return ""
}

type SKBuiltinBufferT struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ILen   *uint32 `protobuf:"varint,1,opt,name=iLen" json:"iLen,omitempty"`
	Buffer []byte  `protobuf:"bytes,2,opt,name=buffer" json:"buffer,omitempty"`
}

func (x *SKBuiltinBufferT) Reset() {
	*x = SKBuiltinBufferT{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wechat_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SKBuiltinBufferT) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SKBuiltinBufferT) ProtoMessage() {}

func (x *SKBuiltinBufferT) ProtoReflect() protoreflect.Message {
	mi := &file_wechat_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SKBuiltinBufferT.ProtoReflect.Descriptor instead.
func (*SKBuiltinBufferT) Descriptor() ([]byte, []int) {
	return file_wechat_proto_rawDescGZIP(), []int{34}
}

func (x *SKBuiltinBufferT) GetILen() uint32 {
	if x != nil && x.ILen != nil {
		return *x.ILen
	}
	return 0
}

func (x *SKBuiltinBufferT) GetBuffer() []byte {
	if x != nil {
		return x.Buffer
	}
	return nil
}

type UploadAppAttachRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BaseRequest     *BaseRequest      `protobuf:"bytes,1,opt,name=BaseRequest" json:"BaseRequest,omitempty"`
	AppId           *string           `protobuf:"bytes,2,opt,name=appId" json:"appId,omitempty"`
	SdkVersion      *uint32           `protobuf:"varint,3,opt,name=sdkVersion" json:"sdkVersion,omitempty"`
	ClientAppDataId *string           `protobuf:"bytes,4,opt,name=clientAppDataId" json:"clientAppDataId,omitempty"`
	UserName        *string           `protobuf:"bytes,5,opt,name=userName" json:"userName,omitempty"`
	TotalLen        *uint32           `protobuf:"varint,6,opt,name=totalLen" json:"totalLen,omitempty"`
	StartPos        *uint32           `protobuf:"varint,7,opt,name=startPos" json:"startPos,omitempty"`
	DataLen         *uint32           `protobuf:"varint,8,opt,name=dataLen" json:"dataLen,omitempty"`
	Data            *SKBuiltinBufferT `protobuf:"bytes,9,opt,name=data" json:"data,omitempty"`
	Type            *uint32           `protobuf:"varint,10,opt,name=type" json:"type,omitempty"`
	Md5             *string           `protobuf:"bytes,11,opt,name=md5" json:"md5,omitempty"`
}

func (x *UploadAppAttachRequest) Reset() {
	*x = UploadAppAttachRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wechat_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UploadAppAttachRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UploadAppAttachRequest) ProtoMessage() {}

func (x *UploadAppAttachRequest) ProtoReflect() protoreflect.Message {
	mi := &file_wechat_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UploadAppAttachRequest.ProtoReflect.Descriptor instead.
func (*UploadAppAttachRequest) Descriptor() ([]byte, []int) {
	return file_wechat_proto_rawDescGZIP(), []int{35}
}

func (x *UploadAppAttachRequest) GetBaseRequest() *BaseRequest {
	if x != nil {
		return x.BaseRequest
	}
	return nil
}

func (x *UploadAppAttachRequest) GetAppId() string {
	if x != nil && x.AppId != nil {
		return *x.AppId
	}
	return ""
}

func (x *UploadAppAttachRequest) GetSdkVersion() uint32 {
	if x != nil && x.SdkVersion != nil {
		return *x.SdkVersion
	}
	return 0
}

func (x *UploadAppAttachRequest) GetClientAppDataId() string {
	if x != nil && x.ClientAppDataId != nil {
		return *x.ClientAppDataId
	}
	return ""
}

func (x *UploadAppAttachRequest) GetUserName() string {
	if x != nil && x.UserName != nil {
		return *x.UserName
	}
	return ""
}

func (x *UploadAppAttachRequest) GetTotalLen() uint32 {
	if x != nil && x.TotalLen != nil {
		return *x.TotalLen
	}
	return 0
}

func (x *UploadAppAttachRequest) GetStartPos() uint32 {
	if x != nil && x.StartPos != nil {
		return *x.StartPos
	}
	return 0
}

func (x *UploadAppAttachRequest) GetDataLen() uint32 {
	if x != nil && x.DataLen != nil {
		return *x.DataLen
	}
	return 0
}

func (x *UploadAppAttachRequest) GetData() *SKBuiltinBufferT {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *UploadAppAttachRequest) GetType() uint32 {
	if x != nil && x.Type != nil {
		return *x.Type
	}
	return 0
}

func (x *UploadAppAttachRequest) GetMd5() string {
	if x != nil && x.Md5 != nil {
		return *x.Md5
	}
	return ""
}

type UploadAppAttachResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BaseResponse    *BaseResponse `protobuf:"bytes,1,opt,name=BaseResponse" json:"BaseResponse,omitempty"`
	AppId           *string       `protobuf:"bytes,2,opt,name=appId" json:"appId,omitempty"`
	MediaId         *string       `protobuf:"bytes,3,opt,name=mediaId" json:"mediaId,omitempty"`
	ClientAppDataId *string       `protobuf:"bytes,4,opt,name=clientAppDataId" json:"clientAppDataId,omitempty"`
	UserName        *string       `protobuf:"bytes,5,opt,name=userName" json:"userName,omitempty"`
	TotalLen        *uint32       `protobuf:"varint,6,opt,name=totalLen" json:"totalLen,omitempty"`
	StartPos        *uint32       `protobuf:"varint,7,opt,name=startPos" json:"startPos,omitempty"`
	DataLen         *uint32       `protobuf:"varint,8,opt,name=dataLen" json:"dataLen,omitempty"`
	CreateTime      *uint64       `protobuf:"varint,9,opt,name=createTime" json:"createTime,omitempty"`
}

func (x *UploadAppAttachResponse) Reset() {
	*x = UploadAppAttachResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wechat_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UploadAppAttachResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UploadAppAttachResponse) ProtoMessage() {}

func (x *UploadAppAttachResponse) ProtoReflect() protoreflect.Message {
	mi := &file_wechat_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UploadAppAttachResponse.ProtoReflect.Descriptor instead.
func (*UploadAppAttachResponse) Descriptor() ([]byte, []int) {
	return file_wechat_proto_rawDescGZIP(), []int{36}
}

func (x *UploadAppAttachResponse) GetBaseResponse() *BaseResponse {
	if x != nil {
		return x.BaseResponse
	}
	return nil
}

func (x *UploadAppAttachResponse) GetAppId() string {
	if x != nil && x.AppId != nil {
		return *x.AppId
	}
	return ""
}

func (x *UploadAppAttachResponse) GetMediaId() string {
	if x != nil && x.MediaId != nil {
		return *x.MediaId
	}
	return ""
}

func (x *UploadAppAttachResponse) GetClientAppDataId() string {
	if x != nil && x.ClientAppDataId != nil {
		return *x.ClientAppDataId
	}
	return ""
}

func (x *UploadAppAttachResponse) GetUserName() string {
	if x != nil && x.UserName != nil {
		return *x.UserName
	}
	return ""
}

func (x *UploadAppAttachResponse) GetTotalLen() uint32 {
	if x != nil && x.TotalLen != nil {
		return *x.TotalLen
	}
	return 0
}

func (x *UploadAppAttachResponse) GetStartPos() uint32 {
	if x != nil && x.StartPos != nil {
		return *x.StartPos
	}
	return 0
}

func (x *UploadAppAttachResponse) GetDataLen() uint32 {
	if x != nil && x.DataLen != nil {
		return *x.DataLen
	}
	return 0
}

func (x *UploadAppAttachResponse) GetCreateTime() uint64 {
	if x != nil && x.CreateTime != nil {
		return *x.CreateTime
	}
	return 0
}

type DownloadVoiceRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MsgId        *uint64      `protobuf:"varint,1,opt,name=msgId" json:"msgId,omitempty"`
	Offset       *uint32      `protobuf:"varint,2,opt,name=offset" json:"offset,omitempty"`
	Length       *uint32      `protobuf:"varint,3,opt,name=length" json:"length,omitempty"`
	ClientMsgId  *string      `protobuf:"bytes,4,opt,name=clientMsgId" json:"clientMsgId,omitempty"`
	BaseRequest  *BaseRequest `protobuf:"bytes,5,opt,name=baseRequest" json:"baseRequest,omitempty"`
	NewMsgId     *uint64      `protobuf:"varint,6,opt,name=newMsgId" json:"newMsgId,omitempty"`
	ChatRoomName *string      `protobuf:"bytes,7,opt,name=chatRoomName" json:"chatRoomName,omitempty"`
	MasterBufId  *int64       `protobuf:"varint,8,opt,name=masterBufId" json:"masterBufId,omitempty"`
}

func (x *DownloadVoiceRequest) Reset() {
	*x = DownloadVoiceRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wechat_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DownloadVoiceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DownloadVoiceRequest) ProtoMessage() {}

func (x *DownloadVoiceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_wechat_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DownloadVoiceRequest.ProtoReflect.Descriptor instead.
func (*DownloadVoiceRequest) Descriptor() ([]byte, []int) {
	return file_wechat_proto_rawDescGZIP(), []int{37}
}

func (x *DownloadVoiceRequest) GetMsgId() uint64 {
	if x != nil && x.MsgId != nil {
		return *x.MsgId
	}
	return 0
}

func (x *DownloadVoiceRequest) GetOffset() uint32 {
	if x != nil && x.Offset != nil {
		return *x.Offset
	}
	return 0
}

func (x *DownloadVoiceRequest) GetLength() uint32 {
	if x != nil && x.Length != nil {
		return *x.Length
	}
	return 0
}

func (x *DownloadVoiceRequest) GetClientMsgId() string {
	if x != nil && x.ClientMsgId != nil {
		return *x.ClientMsgId
	}
	return ""
}

func (x *DownloadVoiceRequest) GetBaseRequest() *BaseRequest {
	if x != nil {
		return x.BaseRequest
	}
	return nil
}

func (x *DownloadVoiceRequest) GetNewMsgId() uint64 {
	if x != nil && x.NewMsgId != nil {
		return *x.NewMsgId
	}
	return 0
}

func (x *DownloadVoiceRequest) GetChatRoomName() string {
	if x != nil && x.ChatRoomName != nil {
		return *x.ChatRoomName
	}
	return ""
}

func (x *DownloadVoiceRequest) GetMasterBufId() int64 {
	if x != nil && x.MasterBufId != nil {
		return *x.MasterBufId
	}
	return 0
}

type DownloadVoiceResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MsgId        *uint32           `protobuf:"varint,1,opt,name=msgId" json:"msgId,omitempty"`
	Offset       *uint32           `protobuf:"varint,2,opt,name=offset" json:"offset,omitempty"`
	Length       *uint32           `protobuf:"varint,3,opt,name=length" json:"length,omitempty"`
	VoiceLength  *uint32           `protobuf:"varint,5,opt,name=voiceLength" json:"voiceLength,omitempty"`
	ClientMsgId  *string           `protobuf:"bytes,6,opt,name=clientMsgId" json:"clientMsgId,omitempty"`
	Data         *SKBuiltinString_ `protobuf:"bytes,7,opt,name=data" json:"data,omitempty"`
	EndFlag      *uint32           `protobuf:"varint,8,opt,name=endFlag" json:"endFlag,omitempty"`
	BaseResponse *BaseResponse     `protobuf:"bytes,9,opt,name=baseResponse" json:"baseResponse,omitempty"`
	CancelFlag   *uint32           `protobuf:"varint,10,opt,name=cancelFlag" json:"cancelFlag,omitempty"`
	NewMsgId     *uint64           `protobuf:"varint,11,opt,name=newMsgId" json:"newMsgId,omitempty"`
}

func (x *DownloadVoiceResponse) Reset() {
	*x = DownloadVoiceResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wechat_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DownloadVoiceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DownloadVoiceResponse) ProtoMessage() {}

func (x *DownloadVoiceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_wechat_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DownloadVoiceResponse.ProtoReflect.Descriptor instead.
func (*DownloadVoiceResponse) Descriptor() ([]byte, []int) {
	return file_wechat_proto_rawDescGZIP(), []int{38}
}

func (x *DownloadVoiceResponse) GetMsgId() uint32 {
	if x != nil && x.MsgId != nil {
		return *x.MsgId
	}
	return 0
}

func (x *DownloadVoiceResponse) GetOffset() uint32 {
	if x != nil && x.Offset != nil {
		return *x.Offset
	}
	return 0
}

func (x *DownloadVoiceResponse) GetLength() uint32 {
	if x != nil && x.Length != nil {
		return *x.Length
	}
	return 0
}

func (x *DownloadVoiceResponse) GetVoiceLength() uint32 {
	if x != nil && x.VoiceLength != nil {
		return *x.VoiceLength
	}
	return 0
}

func (x *DownloadVoiceResponse) GetClientMsgId() string {
	if x != nil && x.ClientMsgId != nil {
		return *x.ClientMsgId
	}
	return ""
}

func (x *DownloadVoiceResponse) GetData() *SKBuiltinString_ {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *DownloadVoiceResponse) GetEndFlag() uint32 {
	if x != nil && x.EndFlag != nil {
		return *x.EndFlag
	}
	return 0
}

func (x *DownloadVoiceResponse) GetBaseResponse() *BaseResponse {
	if x != nil {
		return x.BaseResponse
	}
	return nil
}

func (x *DownloadVoiceResponse) GetCancelFlag() uint32 {
	if x != nil && x.CancelFlag != nil {
		return *x.CancelFlag
	}
	return 0
}

func (x *DownloadVoiceResponse) GetNewMsgId() uint64 {
	if x != nil && x.NewMsgId != nil {
		return *x.NewMsgId
	}
	return 0
}

var File_wechat_proto protoreflect.FileDescriptor

var file_wechat_proto_rawDesc = []byte{
	0x0a, 0x0c, 0x77, 0x65, 0x63, 0x68, 0x61, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x06,
	0x77, 0x65, 0x63, 0x68, 0x61, 0x74, 0x22, 0xb7, 0x01, 0x0a, 0x0b, 0x42, 0x61, 0x73, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x4b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0a, 0x73, 0x65, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x4b, 0x65, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x6e, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x03, 0x75, 0x69, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x08, 0x64, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x49, 0x64, 0x12, 0x24, 0x0a, 0x0d, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x56, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x63, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1e, 0x0a, 0x0a, 0x64, 0x65,
	0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0a,
	0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x63,
	0x65, 0x6e, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x73, 0x63, 0x65, 0x6e, 0x65,
	0x22, 0xb4, 0x01, 0x0a, 0x0f, 0x42, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x50, 0x6c, 0x75, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f,
	0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0a, 0x73, 0x65, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x4b, 0x65, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x03, 0x75, 0x69, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x08, 0x64, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x49, 0x64, 0x12, 0x24, 0x0a, 0x0d, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x56, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x63, 0x6c, 0x69, 0x65,
	0x6e, 0x74, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x73, 0x54,
	0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x73, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x63, 0x65, 0x6e, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x05, 0x73, 0x63, 0x65, 0x6e, 0x65, 0x22, 0x2b, 0x0a, 0x11, 0x53, 0x4b, 0x42, 0x75, 0x69,
	0x6c, 0x74, 0x69, 0x6e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x74, 0x12, 0x16, 0x0a, 0x06,
	0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74,
	0x72, 0x69, 0x6e, 0x67, 0x22, 0x53, 0x0a, 0x0c, 0x42, 0x61, 0x73, 0x65, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x72, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x03, 0x72, 0x65, 0x74, 0x12, 0x31, 0x0a, 0x06, 0x65, 0x72, 0x72, 0x4d, 0x73, 0x67,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x77, 0x65, 0x63, 0x68, 0x61, 0x74, 0x2e,
	0x53, 0x4b, 0x42, 0x75, 0x69, 0x6c, 0x74, 0x69, 0x6e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x5f,
	0x74, 0x52, 0x06, 0x65, 0x72, 0x72, 0x4d, 0x73, 0x67, 0x22, 0xa1, 0x01, 0x0a, 0x1e, 0x4d, 0x4d,
	0x42, 0x69, 0x7a, 0x4a, 0x73, 0x41, 0x70, 0x69, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x4f,
	0x70, 0x65, 0x6e, 0x49, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x35, 0x0a, 0x0b,
	0x42, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x18, 0x01, 0x20, 0x02, 0x28,
	0x0b, 0x32, 0x13, 0x2e, 0x77, 0x65, 0x63, 0x68, 0x61, 0x74, 0x2e, 0x42, 0x61, 0x73, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x0b, 0x42, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x41, 0x70, 0x70, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x41, 0x70, 0x70, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x42, 0x75, 0x73,
	0x69, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x42, 0x75, 0x73, 0x69, 0x49,
	0x64, 0x12, 0x1a, 0x0a, 0x08, 0x55, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x55, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0xee, 0x02,
	0x0a, 0x1f, 0x4d, 0x4d, 0x42, 0x69, 0x7a, 0x4a, 0x73, 0x41, 0x70, 0x69, 0x47, 0x65, 0x74, 0x55,
	0x73, 0x65, 0x72, 0x4f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x38, 0x0a, 0x0c, 0x42, 0x61, 0x73, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x18, 0x01, 0x20, 0x02, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x77, 0x65, 0x63, 0x68, 0x61, 0x74,
	0x2e, 0x42, 0x61, 0x73, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0c, 0x42,
	0x61, 0x73, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x4f,
	0x70, 0x65, 0x6e, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x4f, 0x70, 0x65,
	0x6e, 0x69, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x4e, 0x69, 0x63, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x4e, 0x69, 0x63, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x1e, 0x0a, 0x0a, 0x48, 0x65, 0x61, 0x64, 0x49, 0x6d, 0x67, 0x55, 0x72, 0x6c, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x48, 0x65, 0x61, 0x64, 0x49, 0x6d, 0x67, 0x55, 0x72, 0x6c, 0x12,
	0x12, 0x0a, 0x04, 0x53, 0x69, 0x67, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x53,
	0x69, 0x67, 0x6e, 0x12, 0x26, 0x0a, 0x0e, 0x46, 0x72, 0x69, 0x65, 0x6e, 0x64, 0x52, 0x65, 0x6c,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0e, 0x46, 0x72, 0x69,
	0x65, 0x6e, 0x64, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x31, 0x0a, 0x14, 0x58,
	0x58, 0x58, 0x5f, 0x4e, 0x6f, 0x55, 0x6e, 0x6b, 0x65, 0x79, 0x65, 0x64, 0x4c, 0x69, 0x74, 0x65,
	0x72, 0x61, 0x6c, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x58, 0x58, 0x58, 0x4e, 0x6f,
	0x55, 0x6e, 0x6b, 0x65, 0x79, 0x65, 0x64, 0x4c, 0x69, 0x74, 0x65, 0x72, 0x61, 0x6c, 0x12, 0x29,
	0x0a, 0x10, 0x58, 0x58, 0x58, 0x5f, 0x75, 0x6e, 0x72, 0x65, 0x63, 0x6f, 0x67, 0x6e, 0x69, 0x7a,
	0x65, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x58, 0x58, 0x58, 0x55, 0x6e, 0x72,
	0x65, 0x63, 0x6f, 0x67, 0x6e, 0x69, 0x7a, 0x65, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x58, 0x58, 0x58,
	0x5f, 0x73, 0x69, 0x7a, 0x65, 0x63, 0x61, 0x63, 0x68, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0c, 0x58, 0x58, 0x58, 0x53, 0x69, 0x7a, 0x65, 0x63, 0x61, 0x63, 0x68, 0x65, 0x22, 0x36,
	0x0a, 0x08, 0x42, 0x75, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x4c,
	0x65, 0x6e, 0x18, 0x01, 0x20, 0x02, 0x28, 0x0d, 0x52, 0x04, 0x69, 0x4c, 0x65, 0x6e, 0x12, 0x16,
	0x0a, 0x06, 0x42, 0x75, 0x66, 0x66, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x06,
	0x42, 0x75, 0x66, 0x66, 0x65, 0x72, 0x22, 0x90, 0x01, 0x0a, 0x0a, 0x45, 0x63, 0x64, 0x68, 0x50,
	0x61, 0x63, 0x6b, 0x65, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x54, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20,
	0x02, 0x28, 0x0d, 0x52, 0x04, 0x54, 0x79, 0x70, 0x65, 0x12, 0x22, 0x0a, 0x03, 0x4b, 0x65, 0x79,
	0x18, 0x02, 0x20, 0x02, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x77, 0x65, 0x63, 0x68, 0x61, 0x74, 0x2e,
	0x42, 0x75, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x74, 0x52, 0x03, 0x4b, 0x65, 0x79, 0x12, 0x14, 0x0a,
	0x05, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x03, 0x20, 0x02, 0x28, 0x0c, 0x52, 0x05, 0x54, 0x6f,
	0x6b, 0x65, 0x6e, 0x12, 0x10, 0x0a, 0x03, 0x55, 0x72, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x55, 0x72, 0x6c, 0x12, 0x22, 0x0a, 0x0c, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x44, 0x61, 0x74, 0x61, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0c, 0x50, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x44, 0x61, 0x74, 0x61, 0x22, 0x89, 0x01, 0x0a, 0x15, 0x48, 0x79,
	0x62, 0x72, 0x69, 0x64, 0x44, 0x65, 0x63, 0x72, 0x79, 0x70, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x22, 0x0a, 0x03, 0x4b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x02, 0x28, 0x0b,
	0x32, 0x10, 0x2e, 0x77, 0x65, 0x63, 0x68, 0x61, 0x74, 0x2e, 0x42, 0x75, 0x66, 0x66, 0x65, 0x72,
	0x5f, 0x74, 0x52, 0x03, 0x4b, 0x65, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x54, 0x79, 0x70, 0x65, 0x18,
	0x02, 0x20, 0x02, 0x28, 0x0d, 0x52, 0x04, 0x54, 0x79, 0x70, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x50,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x44, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x02, 0x28,
	0x0c, 0x52, 0x0c, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x44, 0x61, 0x74, 0x61, 0x12,
	0x14, 0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x05,
	0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0xd1, 0x01, 0x0a, 0x11, 0x48, 0x79, 0x62, 0x72, 0x69, 0x64,
	0x45, 0x63, 0x64, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12,
	0x30, 0x0a, 0x0a, 0x53, 0x65, 0x63, 0x45, 0x43, 0x44, 0x48, 0x4b, 0x65, 0x79, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x77, 0x65, 0x63, 0x68, 0x61, 0x74, 0x2e, 0x42, 0x75, 0x66,
	0x66, 0x65, 0x72, 0x5f, 0x74, 0x52, 0x0a, 0x53, 0x65, 0x63, 0x45, 0x43, 0x44, 0x48, 0x4b, 0x65,
	0x79, 0x12, 0x24, 0x0a, 0x0d, 0x72, 0x61, 0x6e, 0x64, 0x6f, 0x6d, 0x6b, 0x65, 0x79, 0x64, 0x61,
	0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0d, 0x72, 0x61, 0x6e, 0x64, 0x6f, 0x6d,
	0x6b, 0x65, 0x79, 0x64, 0x61, 0x74, 0x61, 0x12, 0x30, 0x0a, 0x13, 0x72, 0x61, 0x6e, 0x64, 0x6f,
	0x6d, 0x6b, 0x65, 0x79, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x64, 0x64, 0x61, 0x74, 0x61, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0c, 0x52, 0x13, 0x72, 0x61, 0x6e, 0x64, 0x6f, 0x6d, 0x6b, 0x65, 0x79, 0x65,
	0x78, 0x74, 0x65, 0x6e, 0x64, 0x64, 0x61, 0x74, 0x61, 0x12, 0x1e, 0x0a, 0x0a, 0x65, 0x6e, 0x63,
	0x79, 0x70, 0x74, 0x64, 0x61, 0x74, 0x61, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0a, 0x65,
	0x6e, 0x63, 0x79, 0x70, 0x74, 0x64, 0x61, 0x74, 0x61, 0x22, 0xae, 0x01, 0x0a, 0x12, 0x48, 0x79,
	0x62, 0x72, 0x69, 0x64, 0x45, 0x63, 0x64, 0x68, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x30, 0x0a, 0x0a, 0x53, 0x65, 0x63, 0x45, 0x43, 0x44, 0x48, 0x4b, 0x65, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x77, 0x65, 0x63, 0x68, 0x61, 0x74, 0x2e, 0x42, 0x75,
	0x66, 0x66, 0x65, 0x72, 0x5f, 0x74, 0x52, 0x0a, 0x53, 0x65, 0x63, 0x45, 0x43, 0x44, 0x48, 0x4b,
	0x65, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x63, 0x72, 0x79, 0x70,
	0x74, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0b, 0x64, 0x65, 0x63,
	0x72, 0x79, 0x70, 0x74, 0x64, 0x61, 0x74, 0x61, 0x12, 0x30, 0x0a, 0x13, 0x72, 0x61, 0x6e, 0x64,
	0x6f, 0x6d, 0x6b, 0x65, 0x79, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x64, 0x64, 0x61, 0x74, 0x61, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x13, 0x72, 0x61, 0x6e, 0x64, 0x6f, 0x6d, 0x6b, 0x65, 0x79,
	0x65, 0x78, 0x74, 0x65, 0x6e, 0x64, 0x64, 0x61, 0x74, 0x61, 0x22, 0x47, 0x0a, 0x07, 0x45, 0x43,
	0x44, 0x48, 0x4b, 0x65, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6e, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x03, 0x6e, 0x69, 0x64, 0x12, 0x2a, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x77, 0x65, 0x63, 0x68, 0x61, 0x74, 0x2e, 0x53, 0x4b,
	0x42, 0x75, 0x69, 0x6c, 0x74, 0x69, 0x6e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x52, 0x03,
	0x6b, 0x65, 0x79, 0x22, 0x3c, 0x0a, 0x10, 0x53, 0x4b, 0x42, 0x75, 0x69, 0x6c, 0x74, 0x69, 0x6e,
	0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x12, 0x10, 0x0a, 0x03, 0x6c, 0x65, 0x6e, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x6c, 0x65, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x62, 0x75, 0x66,
	0x66, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x06, 0x62, 0x75, 0x66, 0x66, 0x65,
	0x72, 0x22, 0xd1, 0x01, 0x0a, 0x14, 0x4d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x41, 0x75, 0x74, 0x68,
	0x52, 0x73, 0x61, 0x52, 0x65, 0x71, 0x44, 0x61, 0x74, 0x61, 0x12, 0x40, 0x0a, 0x0e, 0x72, 0x61,
	0x6e, 0x64, 0x6f, 0x6d, 0x45, 0x6e, 0x63, 0x72, 0x79, 0x4b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x18, 0x2e, 0x77, 0x65, 0x63, 0x68, 0x61, 0x74, 0x2e, 0x53, 0x4b, 0x42, 0x75,
	0x69, 0x6c, 0x74, 0x69, 0x6e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x52, 0x0e, 0x72, 0x61,
	0x6e, 0x64, 0x6f, 0x6d, 0x45, 0x6e, 0x63, 0x72, 0x79, 0x4b, 0x65, 0x79, 0x12, 0x35, 0x0a, 0x0d,
	0x63, 0x6c, 0x69, 0x50, 0x75, 0x62, 0x45, 0x63, 0x64, 0x68, 0x6b, 0x65, 0x79, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x77, 0x65, 0x63, 0x68, 0x61, 0x74, 0x2e, 0x45, 0x43, 0x44,
	0x48, 0x4b, 0x65, 0x79, 0x52, 0x0d, 0x63, 0x6c, 0x69, 0x50, 0x75, 0x62, 0x45, 0x63, 0x64, 0x68,
	0x6b, 0x65, 0x79, 0x12, 0x1a, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x10, 0x0a, 0x03, 0x70, 0x77, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x70, 0x77,
	0x64, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x77, 0x64, 0x32, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x70, 0x77, 0x64, 0x32, 0x22, 0x9d, 0x01, 0x0a, 0x11, 0x57, 0x54, 0x4c, 0x6f, 0x67, 0x69,
	0x6e, 0x49, 0x6d, 0x67, 0x52, 0x65, 0x71, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x17, 0x0a, 0x07, 0x69,
	0x6d, 0x67, 0x5f, 0x73, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x69, 0x6d,
	0x67, 0x53, 0x69, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x69, 0x6d, 0x67, 0x5f, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x69, 0x6d, 0x67, 0x43, 0x6f, 0x64, 0x65, 0x12,
	0x26, 0x0a, 0x0f, 0x69, 0x6d, 0x67, 0x5f, 0x65, 0x6e, 0x63, 0x72, 0x79, 0x70, 0x74, 0x5f, 0x6b,
	0x65, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x69, 0x6d, 0x67, 0x45, 0x6e, 0x63,
	0x72, 0x79, 0x70, 0x74, 0x4b, 0x65, 0x79, 0x12, 0x2c, 0x0a, 0x04, 0x6b, 0x73, 0x69, 0x64, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x77, 0x65, 0x63, 0x68, 0x61, 0x74, 0x2e, 0x53,
	0x4b, 0x42, 0x75, 0x69, 0x6c, 0x74, 0x69, 0x6e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x52,
	0x04, 0x6b, 0x73, 0x69, 0x64, 0x22, 0x67, 0x0a, 0x13, 0x57, 0x78, 0x56, 0x65, 0x72, 0x69, 0x66,
	0x79, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x71, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x29, 0x0a, 0x10,
	0x76, 0x65, 0x72, 0x69, 0x66, 0x79, 0x5f, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x76, 0x65, 0x72, 0x69, 0x66, 0x79, 0x53, 0x69,
	0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x76, 0x65, 0x72, 0x69, 0x66,
	0x79, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0d, 0x76, 0x65, 0x72, 0x69, 0x66, 0x79, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x22, 0xc9,
	0x03, 0x0a, 0x0f, 0x42, 0x61, 0x73, 0x65, 0x41, 0x75, 0x74, 0x68, 0x52, 0x65, 0x71, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x43, 0x0a, 0x11, 0x77, 0x74, 0x5f, 0x6c, 0x6f, 0x67, 0x69, 0x6e, 0x5f, 0x72,
	0x65, 0x71, 0x5f, 0x62, 0x75, 0x66, 0x66, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e,
	0x77, 0x65, 0x63, 0x68, 0x61, 0x74, 0x2e, 0x53, 0x4b, 0x42, 0x75, 0x69, 0x6c, 0x74, 0x69, 0x6e,
	0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x52, 0x0e, 0x77, 0x74, 0x4c, 0x6f, 0x67, 0x69, 0x6e,
	0x52, 0x65, 0x71, 0x42, 0x75, 0x66, 0x66, 0x12, 0x4b, 0x0a, 0x15, 0x77, 0x74, 0x5f, 0x6c, 0x6f,
	0x67, 0x69, 0x6e, 0x5f, 0x69, 0x6d, 0x67, 0x5f, 0x72, 0x65, 0x71, 0x5f, 0x69, 0x6e, 0x66, 0x6f,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x77, 0x65, 0x63, 0x68, 0x61, 0x74, 0x2e,
	0x57, 0x54, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x49, 0x6d, 0x67, 0x52, 0x65, 0x71, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x11, 0x77, 0x74, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x49, 0x6d, 0x67, 0x52, 0x65, 0x71,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x51, 0x0a, 0x17, 0x77, 0x78, 0x5f, 0x76, 0x65, 0x72, 0x69, 0x66,
	0x79, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x5f, 0x72, 0x65, 0x71, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x77, 0x65, 0x63, 0x68, 0x61, 0x74, 0x2e, 0x57,
	0x78, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x71, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x13, 0x77, 0x78, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x43, 0x6f, 0x64, 0x65,
	0x52, 0x65, 0x71, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x44, 0x0a, 0x11, 0x63, 0x6c, 0x69, 0x64, 0x62,
	0x5f, 0x65, 0x6e, 0x63, 0x72, 0x79, 0x70, 0x74, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x18, 0x2e, 0x77, 0x65, 0x63, 0x68, 0x61, 0x74, 0x2e, 0x53, 0x4b, 0x42, 0x75,
	0x69, 0x6c, 0x74, 0x69, 0x6e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x52, 0x0f, 0x63, 0x6c,
	0x69, 0x64, 0x62, 0x45, 0x6e, 0x63, 0x72, 0x79, 0x70, 0x74, 0x4b, 0x65, 0x79, 0x12, 0x46, 0x0a,
	0x12, 0x63, 0x6c, 0x69, 0x64, 0x62, 0x5f, 0x65, 0x6e, 0x63, 0x72, 0x79, 0x70, 0x74, 0x5f, 0x69,
	0x6e, 0x66, 0x6f, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x77, 0x65, 0x63, 0x68,
	0x61, 0x74, 0x2e, 0x53, 0x4b, 0x42, 0x75, 0x69, 0x6c, 0x74, 0x69, 0x6e, 0x53, 0x74, 0x72, 0x69,
	0x6e, 0x67, 0x5f, 0x52, 0x10, 0x63, 0x6c, 0x69, 0x64, 0x62, 0x45, 0x6e, 0x63, 0x72, 0x79, 0x70,
	0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x22, 0x0a, 0x0d, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x72, 0x65,
	0x71, 0x5f, 0x66, 0x6c, 0x61, 0x67, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0b, 0x61, 0x75,
	0x74, 0x68, 0x52, 0x65, 0x71, 0x46, 0x6c, 0x61, 0x67, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x75, 0x74,
	0x68, 0x5f, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x61, 0x75, 0x74, 0x68, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x22, 0xbe, 0x06, 0x0a, 0x14, 0x4d,
	0x61, 0x6e, 0x75, 0x61, 0x6c, 0x41, 0x75, 0x74, 0x68, 0x41, 0x65, 0x73, 0x52, 0x65, 0x71, 0x44,
	0x61, 0x74, 0x61, 0x12, 0x35, 0x0a, 0x0b, 0x62, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x77, 0x65, 0x63, 0x68, 0x61,
	0x74, 0x2e, 0x42, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x0b, 0x62,
	0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x39, 0x0a, 0x0b, 0x62, 0x61,
	0x73, 0x65, 0x52, 0x65, 0x71, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x17, 0x2e, 0x77, 0x65, 0x63, 0x68, 0x61, 0x74, 0x2e, 0x42, 0x61, 0x73, 0x65, 0x41, 0x75, 0x74,
	0x68, 0x52, 0x65, 0x71, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0b, 0x62, 0x61, 0x73, 0x65, 0x52, 0x65,
	0x71, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x6d, 0x65, 0x69, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x69, 0x6d, 0x65, 0x69, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x6f, 0x66,
	0x74, 0x54, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x6f, 0x66,
	0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x62, 0x75, 0x69, 0x6c, 0x74, 0x69, 0x6e,
	0x49, 0x70, 0x73, 0x65, 0x71, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0c, 0x62, 0x75, 0x69,
	0x6c, 0x74, 0x69, 0x6e, 0x49, 0x70, 0x73, 0x65, 0x71, 0x12, 0x20, 0x0a, 0x0b, 0x63, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x53, 0x65, 0x71, 0x49, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x53, 0x65, 0x71, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x73,
	0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x64, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x64,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x64, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x64,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x61, 0x6e,
	0x67, 0x75, 0x61, 0x67, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x61, 0x6e,
	0x67, 0x75, 0x61, 0x67, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x74, 0x69, 0x6d, 0x65, 0x5a, 0x6f, 0x6e,
	0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x74, 0x69, 0x6d, 0x65, 0x5a, 0x6f, 0x6e,
	0x65, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x18, 0x0d, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x12, 0x1c, 0x0a, 0x09, 0x74,
	0x69, 0x6d, 0x65, 0x53, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09,
	0x74, 0x69, 0x6d, 0x65, 0x53, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x42, 0x72, 0x61, 0x6e, 0x64, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x42, 0x72, 0x61, 0x6e, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x64,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x16, 0x0a,
	0x06, 0x6f, 0x73, 0x74, 0x79, 0x70, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f,
	0x73, 0x74, 0x79, 0x70, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x72, 0x65, 0x61, 0x6c, 0x43, 0x6f, 0x75,
	0x6e, 0x74, 0x72, 0x79, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x72, 0x65, 0x61, 0x6c,
	0x43, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x1a, 0x0a, 0x08, 0x62, 0x75, 0x6e, 0x64, 0x6c,
	0x65, 0x49, 0x64, 0x18, 0x13, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x62, 0x75, 0x6e, 0x64, 0x6c,
	0x65, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x61, 0x64, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18,
	0x14, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x61, 0x64, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12,
	0x1c, 0x0a, 0x09, 0x69, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x56, 0x65, 0x72, 0x18, 0x15, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x69, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x56, 0x65, 0x72, 0x12, 0x1c, 0x0a,
	0x09, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x54, 0x79, 0x70, 0x65, 0x18, 0x16, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x09, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x42, 0x0a, 0x0f, 0x63,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x44, 0x61, 0x74, 0x61, 0x18, 0x17,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x77, 0x65, 0x63, 0x68, 0x61, 0x74, 0x2e, 0x53, 0x4b,
	0x42, 0x75, 0x69, 0x6c, 0x74, 0x69, 0x6e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x52, 0x0f,
	0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x44, 0x61, 0x74, 0x61, 0x12,
	0x3a, 0x0a, 0x0b, 0x65, 0x78, 0x74, 0x53, 0x70, 0x61, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x18,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x77, 0x65, 0x63, 0x68, 0x61, 0x74, 0x2e, 0x53, 0x4b,
	0x42, 0x75, 0x69, 0x6c, 0x74, 0x69, 0x6e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x52, 0x0b,
	0x65, 0x78, 0x74, 0x53, 0x70, 0x61, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x4b, 0x0a, 0x0d, 0x54,
	0x72, 0x75, 0x73, 0x74, 0x53, 0x6f, 0x66, 0x74, 0x44, 0x61, 0x74, 0x61, 0x12, 0x1e, 0x0a, 0x0a,
	0x53, 0x6f, 0x66, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x53, 0x6f, 0x66, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x1a, 0x0a, 0x08,
	0x53, 0x6f, 0x66, 0x74, 0x44, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x08,
	0x53, 0x6f, 0x66, 0x74, 0x44, 0x61, 0x74, 0x61, 0x22, 0x86, 0x01, 0x0a, 0x11, 0x54, 0x72, 0x75,
	0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x31,
	0x0a, 0x08, 0x53, 0x6f, 0x66, 0x74, 0x44, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x15, 0x2e, 0x77, 0x65, 0x63, 0x68, 0x61, 0x74, 0x2e, 0x54, 0x72, 0x75, 0x73, 0x74, 0x53,
	0x6f, 0x66, 0x74, 0x44, 0x61, 0x74, 0x61, 0x52, 0x08, 0x53, 0x6f, 0x66, 0x74, 0x44, 0x61, 0x74,
	0x61, 0x12, 0x20, 0x0a, 0x0b, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x54, 0x6f,
	0x6b, 0x65, 0x6e, 0x12, 0x1c, 0x0a, 0x09, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x22, 0x8e, 0x01, 0x0a, 0x09, 0x54, 0x72, 0x75, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x12,
	0x38, 0x0a, 0x0c, 0x42, 0x61, 0x73, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18,
	0x01, 0x20, 0x02, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x77, 0x65, 0x63, 0x68, 0x61, 0x74, 0x2e, 0x42,
	0x61, 0x73, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0c, 0x42, 0x61, 0x73,
	0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x47, 0x0a, 0x11, 0x54, 0x72, 0x75,
	0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x44, 0x61, 0x74, 0x61, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x77, 0x65, 0x63, 0x68, 0x61, 0x74, 0x2e, 0x54, 0x72,
	0x75, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x44, 0x61, 0x74, 0x61, 0x52,
	0x11, 0x54, 0x72, 0x75, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x44, 0x61,
	0x74, 0x61, 0x22, 0x63, 0x0a, 0x0b, 0x41, 0x75, 0x74, 0x6f, 0x41, 0x75, 0x74, 0x68, 0x4b, 0x65,
	0x79, 0x12, 0x30, 0x0a, 0x0a, 0x45, 0x6e, 0x63, 0x72, 0x79, 0x70, 0x74, 0x4b, 0x65, 0x79, 0x18,
	0x01, 0x20, 0x02, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x77, 0x65, 0x63, 0x68, 0x61, 0x74, 0x2e, 0x42,
	0x75, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x74, 0x52, 0x0a, 0x45, 0x6e, 0x63, 0x72, 0x79, 0x70, 0x74,
	0x4b, 0x65, 0x79, 0x12, 0x22, 0x0a, 0x03, 0x4b, 0x65, 0x79, 0x18, 0x02, 0x20, 0x02, 0x28, 0x0b,
	0x32, 0x10, 0x2e, 0x77, 0x65, 0x63, 0x68, 0x61, 0x74, 0x2e, 0x42, 0x75, 0x66, 0x66, 0x65, 0x72,
	0x5f, 0x74, 0x52, 0x03, 0x4b, 0x65, 0x79, 0x22, 0x87, 0x01, 0x0a, 0x12, 0x41, 0x75, 0x74, 0x6f,
	0x41, 0x75, 0x74, 0x68, 0x52, 0x73, 0x61, 0x52, 0x65, 0x71, 0x44, 0x61, 0x74, 0x61, 0x12, 0x40,
	0x0a, 0x0f, 0x61, 0x65, 0x73, 0x5f, 0x65, 0x6e, 0x63, 0x72, 0x79, 0x70, 0x74, 0x5f, 0x6b, 0x65,
	0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x77, 0x65, 0x63, 0x68, 0x61, 0x74,
	0x2e, 0x53, 0x4b, 0x42, 0x75, 0x69, 0x6c, 0x74, 0x69, 0x6e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67,
	0x5f, 0x52, 0x0d, 0x61, 0x65, 0x73, 0x45, 0x6e, 0x63, 0x72, 0x79, 0x70, 0x74, 0x4b, 0x65, 0x79,
	0x12, 0x2f, 0x0a, 0x0a, 0x70, 0x75, 0x62, 0x45, 0x63, 0x64, 0x68, 0x4b, 0x65, 0x79, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x77, 0x65, 0x63, 0x68, 0x61, 0x74, 0x2e, 0x45, 0x43,
	0x44, 0x48, 0x4b, 0x65, 0x79, 0x52, 0x0a, 0x70, 0x75, 0x62, 0x45, 0x63, 0x64, 0x68, 0x4b, 0x65,
	0x79, 0x22, 0xf8, 0x04, 0x0a, 0x12, 0x41, 0x75, 0x74, 0x6f, 0x41, 0x75, 0x74, 0x68, 0x41, 0x65,
	0x73, 0x52, 0x65, 0x71, 0x44, 0x61, 0x74, 0x61, 0x12, 0x3a, 0x0a, 0x0c, 0x62, 0x61, 0x73, 0x65,
	0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17,
	0x2e, 0x77, 0x65, 0x63, 0x68, 0x61, 0x74, 0x2e, 0x42, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x50, 0x6c, 0x75, 0x73, 0x52, 0x0b, 0x62, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x3b, 0x0a, 0x0d, 0x62, 0x61, 0x73, 0x65, 0x5f, 0x72, 0x65, 0x71,
	0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x77, 0x65,
	0x63, 0x68, 0x61, 0x74, 0x2e, 0x42, 0x61, 0x73, 0x65, 0x41, 0x75, 0x74, 0x68, 0x52, 0x65, 0x71,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0b, 0x62, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x3c, 0x0a, 0x0d, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x6b,
	0x65, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x77, 0x65, 0x63, 0x68, 0x61,
	0x74, 0x2e, 0x53, 0x4b, 0x42, 0x75, 0x69, 0x6c, 0x74, 0x69, 0x6e, 0x53, 0x74, 0x72, 0x69, 0x6e,
	0x67, 0x5f, 0x52, 0x0b, 0x61, 0x75, 0x74, 0x6f, 0x41, 0x75, 0x74, 0x68, 0x4b, 0x65, 0x79, 0x12,
	0x12, 0x0a, 0x04, 0x69, 0x6d, 0x65, 0x69, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x69,
	0x6d, 0x65, 0x69, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x6f, 0x66, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x6f, 0x66, 0x74, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x24, 0x0a, 0x0e, 0x62, 0x75, 0x69, 0x6c, 0x74, 0x69, 0x6e, 0x5f, 0x69, 0x70, 0x5f, 0x73,
	0x65, 0x71, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0c, 0x62, 0x75, 0x69, 0x6c, 0x74, 0x69,
	0x6e, 0x49, 0x70, 0x53, 0x65, 0x71, 0x12, 0x22, 0x0a, 0x0d, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x5f, 0x73, 0x65, 0x71, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x53, 0x65, 0x71, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x69,
	0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73,
	0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x64, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x64,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x64, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x64,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x61, 0x6e,
	0x67, 0x75, 0x61, 0x67, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x61, 0x6e,
	0x67, 0x75, 0x61, 0x67, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x7a, 0x6f,
	0x6e, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x74, 0x69, 0x6d, 0x65, 0x5a, 0x6f,
	0x6e, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x18, 0x0d, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x12, 0x42, 0x0a, 0x0f,
	0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x44, 0x61, 0x74, 0x61, 0x18,
	0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x77, 0x65, 0x63, 0x68, 0x61, 0x74, 0x2e, 0x53,
	0x4b, 0x42, 0x75, 0x69, 0x6c, 0x74, 0x69, 0x6e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x52,
	0x0f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x44, 0x61, 0x74, 0x61,
	0x12, 0x3a, 0x0a, 0x0b, 0x65, 0x78, 0x74, 0x53, 0x70, 0x61, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x18,
	0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x77, 0x65, 0x63, 0x68, 0x61, 0x74, 0x2e, 0x53,
	0x4b, 0x42, 0x75, 0x69, 0x6c, 0x74, 0x69, 0x6e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x52,
	0x0b, 0x65, 0x78, 0x74, 0x53, 0x70, 0x61, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x8d, 0x01, 0x0a,
	0x0f, 0x41, 0x75, 0x74, 0x6f, 0x41, 0x75, 0x74, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x3c, 0x0a, 0x0c, 0x72, 0x73, 0x61, 0x5f, 0x72, 0x65, 0x71, 0x5f, 0x64, 0x61, 0x74, 0x61,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x77, 0x65, 0x63, 0x68, 0x61, 0x74, 0x2e,
	0x41, 0x75, 0x74, 0x6f, 0x41, 0x75, 0x74, 0x68, 0x52, 0x73, 0x61, 0x52, 0x65, 0x71, 0x44, 0x61,
	0x74, 0x61, 0x52, 0x0a, 0x72, 0x73, 0x61, 0x52, 0x65, 0x71, 0x44, 0x61, 0x74, 0x61, 0x12, 0x3c,
	0x0a, 0x0c, 0x61, 0x65, 0x73, 0x5f, 0x72, 0x65, 0x71, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x77, 0x65, 0x63, 0x68, 0x61, 0x74, 0x2e, 0x41, 0x75,
	0x74, 0x6f, 0x41, 0x75, 0x74, 0x68, 0x41, 0x65, 0x73, 0x52, 0x65, 0x71, 0x44, 0x61, 0x74, 0x61,
	0x52, 0x0a, 0x61, 0x65, 0x73, 0x52, 0x65, 0x71, 0x44, 0x61, 0x74, 0x61, 0x22, 0x84, 0x04, 0x0a,
	0x0c, 0x57, 0x43, 0x45, 0x78, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x4e, 0x65, 0x77, 0x12, 0x26, 0x0a,
	0x05, 0x57, 0x63, 0x73, 0x74, 0x66, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x77,
	0x65, 0x63, 0x68, 0x61, 0x74, 0x2e, 0x42, 0x75, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x74, 0x52, 0x05,
	0x57, 0x63, 0x73, 0x74, 0x66, 0x12, 0x26, 0x0a, 0x05, 0x57, 0x63, 0x73, 0x74, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x77, 0x65, 0x63, 0x68, 0x61, 0x74, 0x2e, 0x42, 0x75,
	0x66, 0x66, 0x65, 0x72, 0x5f, 0x74, 0x52, 0x05, 0x57, 0x63, 0x73, 0x74, 0x65, 0x12, 0x28, 0x0a,
	0x06, 0x43, 0x63, 0x44, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e,
	0x77, 0x65, 0x63, 0x68, 0x61, 0x74, 0x2e, 0x42, 0x75, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x74, 0x52,
	0x06, 0x43, 0x63, 0x44, 0x61, 0x74, 0x61, 0x12, 0x34, 0x0a, 0x0c, 0x55, 0x73, 0x65, 0x72, 0x41,
	0x74, 0x74, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e,
	0x77, 0x65, 0x63, 0x68, 0x61, 0x74, 0x2e, 0x42, 0x75, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x74, 0x52,
	0x0c, 0x55, 0x73, 0x65, 0x72, 0x41, 0x74, 0x74, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x38, 0x0a,
	0x0e, 0x41, 0x63, 0x67, 0x69, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x77, 0x65, 0x63, 0x68, 0x61, 0x74, 0x2e, 0x42,
	0x75, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x74, 0x52, 0x0e, 0x41, 0x63, 0x67, 0x69, 0x44, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x30, 0x0a, 0x0a, 0x41, 0x63, 0x67, 0x69, 0x54,
	0x75, 0x72, 0x69, 0x6e, 0x67, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x77, 0x65,
	0x63, 0x68, 0x61, 0x74, 0x2e, 0x42, 0x75, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x74, 0x52, 0x0a, 0x41,
	0x63, 0x67, 0x69, 0x54, 0x75, 0x72, 0x69, 0x6e, 0x67, 0x12, 0x32, 0x0a, 0x0b, 0x44, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10,
	0x2e, 0x77, 0x65, 0x63, 0x68, 0x61, 0x74, 0x2e, 0x42, 0x75, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x74,
	0x52, 0x0b, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x30, 0x0a,
	0x0a, 0x42, 0x65, 0x68, 0x61, 0x76, 0x69, 0x6f, 0x72, 0x49, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x10, 0x2e, 0x77, 0x65, 0x63, 0x68, 0x61, 0x74, 0x2e, 0x42, 0x75, 0x66, 0x66, 0x65,
	0x72, 0x5f, 0x74, 0x52, 0x0a, 0x42, 0x65, 0x68, 0x61, 0x76, 0x69, 0x6f, 0x72, 0x49, 0x64, 0x12,
	0x38, 0x0a, 0x0e, 0x49, 0x6f, 0x73, 0x74, 0x75, 0x72, 0x69, 0x6e, 0x67, 0x48, 0x75, 0x6d, 0x61,
	0x6e, 0x18, 0x65, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x77, 0x65, 0x63, 0x68, 0x61, 0x74,
	0x2e, 0x42, 0x75, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x74, 0x52, 0x0e, 0x49, 0x6f, 0x73, 0x74, 0x75,
	0x72, 0x69, 0x6e, 0x67, 0x48, 0x75, 0x6d, 0x61, 0x6e, 0x12, 0x38, 0x0a, 0x0e, 0x49, 0x6f, 0x73,
	0x74, 0x75, 0x72, 0x69, 0x6e, 0x67, 0x4f, 0x77, 0x6e, 0x65, 0x72, 0x18, 0x66, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x10, 0x2e, 0x77, 0x65, 0x63, 0x68, 0x61, 0x74, 0x2e, 0x42, 0x75, 0x66, 0x66, 0x65,
	0x72, 0x5f, 0x74, 0x52, 0x0e, 0x49, 0x6f, 0x73, 0x74, 0x75, 0x72, 0x69, 0x6e, 0x67, 0x4f, 0x77,
	0x6e, 0x65, 0x72, 0x22, 0x42, 0x0a, 0x08, 0x46, 0x69, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x1a, 0x0a, 0x08, 0x46, 0x69, 0x6c, 0x65, 0x70, 0x61, 0x74, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x46, 0x69, 0x6c, 0x65, 0x70, 0x61, 0x74, 0x68, 0x12, 0x1a, 0x0a, 0x08, 0x46,
	0x69, 0x6c, 0x65, 0x75, 0x75, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x46,
	0x69, 0x6c, 0x65, 0x75, 0x75, 0x69, 0x64, 0x22, 0xc4, 0x14, 0x0a, 0x0c, 0x53, 0x70, 0x61, 0x6d,
	0x44, 0x61, 0x74, 0x61, 0x42, 0x6f, 0x64, 0x79, 0x12, 0x1a, 0x0a, 0x08, 0x55, 0x6e, 0x4b, 0x6e,
	0x6f, 0x77, 0x6e, 0x31, 0x18, 0x01, 0x20, 0x02, 0x28, 0x05, 0x52, 0x08, 0x55, 0x6e, 0x4b, 0x6e,
	0x6f, 0x77, 0x6e, 0x31, 0x12, 0x1c, 0x0a, 0x09, 0x54, 0x69, 0x6d, 0x65, 0x53, 0x74, 0x61, 0x6d,
	0x70, 0x18, 0x02, 0x20, 0x02, 0x28, 0x0d, 0x52, 0x09, 0x54, 0x69, 0x6d, 0x65, 0x53, 0x74, 0x61,
	0x6d, 0x70, 0x12, 0x18, 0x0a, 0x07, 0x4b, 0x65, 0x79, 0x48, 0x61, 0x73, 0x68, 0x18, 0x03, 0x20,
	0x02, 0x28, 0x05, 0x52, 0x07, 0x4b, 0x65, 0x79, 0x48, 0x61, 0x73, 0x68, 0x12, 0x12, 0x0a, 0x04,
	0x59, 0x65, 0x73, 0x31, 0x18, 0x0b, 0x20, 0x02, 0x28, 0x09, 0x52, 0x04, 0x59, 0x65, 0x73, 0x31,
	0x12, 0x12, 0x0a, 0x04, 0x59, 0x65, 0x73, 0x32, 0x18, 0x0c, 0x20, 0x02, 0x28, 0x09, 0x52, 0x04,
	0x59, 0x65, 0x73, 0x32, 0x12, 0x1e, 0x0a, 0x0a, 0x49, 0x6f, 0x73, 0x56, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x18, 0x0d, 0x20, 0x02, 0x28, 0x09, 0x52, 0x0a, 0x49, 0x6f, 0x73, 0x56, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1e, 0x0a, 0x0a, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x18, 0x0e, 0x20, 0x02, 0x28, 0x09, 0x52, 0x0a, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x55, 0x6e, 0x4b, 0x6e, 0x6f, 0x77, 0x6e, 0x32,
	0x18, 0x0f, 0x20, 0x02, 0x28, 0x05, 0x52, 0x08, 0x55, 0x6e, 0x4b, 0x6e, 0x6f, 0x77, 0x6e, 0x32,
	0x12, 0x30, 0x0a, 0x13, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x46, 0x6f,
	0x72, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x18, 0x10, 0x20, 0x02, 0x28, 0x09, 0x52, 0x13, 0x49,
	0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x46, 0x6f, 0x72, 0x56, 0x65, 0x6e, 0x64,
	0x6f, 0x72, 0x12, 0x34, 0x0a, 0x15, 0x41, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x69, 0x6e,
	0x67, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x18, 0x11, 0x20, 0x02, 0x28,
	0x09, 0x52, 0x15, 0x41, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x69, 0x6e, 0x67, 0x49, 0x64,
	0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x12, 0x18, 0x0a, 0x07, 0x43, 0x61, 0x72, 0x72,
	0x69, 0x65, 0x72, 0x18, 0x12, 0x20, 0x02, 0x28, 0x09, 0x52, 0x07, 0x43, 0x61, 0x72, 0x72, 0x69,
	0x65, 0x72, 0x12, 0x20, 0x0a, 0x0b, 0x42, 0x61, 0x74, 0x74, 0x65, 0x72, 0x79, 0x49, 0x6e, 0x66,
	0x6f, 0x18, 0x13, 0x20, 0x02, 0x28, 0x05, 0x52, 0x0b, 0x42, 0x61, 0x74, 0x74, 0x65, 0x72, 0x79,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x20, 0x0a, 0x0b, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x4e,
	0x61, 0x6d, 0x65, 0x18, 0x14, 0x20, 0x02, 0x28, 0x09, 0x52, 0x0b, 0x4e, 0x65, 0x74, 0x77, 0x6f,
	0x72, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x4e, 0x65, 0x74, 0x54, 0x79, 0x70,
	0x65, 0x18, 0x15, 0x20, 0x02, 0x28, 0x05, 0x52, 0x07, 0x4e, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x20, 0x0a, 0x0b, 0x41, 0x70, 0x70, 0x42, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x49, 0x64, 0x18,
	0x16, 0x20, 0x02, 0x28, 0x09, 0x52, 0x0b, 0x41, 0x70, 0x70, 0x42, 0x75, 0x6e, 0x64, 0x6c, 0x65,
	0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65,
	0x18, 0x17, 0x20, 0x02, 0x28, 0x09, 0x52, 0x0a, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x55, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x18,
	0x20, 0x02, 0x28, 0x09, 0x52, 0x08, 0x55, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1a,
	0x0a, 0x08, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x33, 0x18, 0x19, 0x20, 0x02, 0x28, 0x03,
	0x52, 0x08, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x33, 0x12, 0x1a, 0x0a, 0x08, 0x55, 0x6e,
	0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x34, 0x18, 0x1a, 0x20, 0x02, 0x28, 0x03, 0x52, 0x08, 0x55, 0x6e,
	0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x34, 0x12, 0x1a, 0x0a, 0x08, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77,
	0x6e, 0x35, 0x18, 0x1b, 0x20, 0x02, 0x28, 0x05, 0x52, 0x08, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77,
	0x6e, 0x35, 0x12, 0x1a, 0x0a, 0x08, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x36, 0x18, 0x1c,
	0x20, 0x02, 0x28, 0x05, 0x52, 0x08, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x36, 0x12, 0x12,
	0x0a, 0x04, 0x4c, 0x61, 0x6e, 0x67, 0x18, 0x1d, 0x20, 0x02, 0x28, 0x09, 0x52, 0x04, 0x4c, 0x61,
	0x6e, 0x67, 0x12, 0x18, 0x0a, 0x07, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x18, 0x1e, 0x20,
	0x02, 0x28, 0x09, 0x52, 0x07, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x1a, 0x0a, 0x08,
	0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x37, 0x18, 0x1f, 0x20, 0x02, 0x28, 0x05, 0x52, 0x08,
	0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x37, 0x12, 0x20, 0x0a, 0x0b, 0x44, 0x6f, 0x63, 0x75,
	0x6d, 0x65, 0x6e, 0x74, 0x44, 0x69, 0x72, 0x18, 0x20, 0x20, 0x02, 0x28, 0x09, 0x52, 0x0b, 0x44,
	0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x69, 0x72, 0x12, 0x1a, 0x0a, 0x08, 0x55, 0x6e,
	0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x38, 0x18, 0x21, 0x20, 0x02, 0x28, 0x05, 0x52, 0x08, 0x55, 0x6e,
	0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x38, 0x12, 0x1a, 0x0a, 0x08, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77,
	0x6e, 0x39, 0x18, 0x22, 0x20, 0x02, 0x28, 0x05, 0x52, 0x08, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77,
	0x6e, 0x39, 0x12, 0x18, 0x0a, 0x07, 0x48, 0x65, 0x61, 0x64, 0x4d, 0x44, 0x35, 0x18, 0x23, 0x20,
	0x02, 0x28, 0x09, 0x52, 0x07, 0x48, 0x65, 0x61, 0x64, 0x4d, 0x44, 0x35, 0x12, 0x18, 0x0a, 0x07,
	0x41, 0x70, 0x70, 0x55, 0x55, 0x49, 0x44, 0x18, 0x24, 0x20, 0x02, 0x28, 0x09, 0x52, 0x07, 0x41,
	0x70, 0x70, 0x55, 0x55, 0x49, 0x44, 0x12, 0x1e, 0x0a, 0x0a, 0x53, 0x79, 0x73, 0x6c, 0x6f, 0x67,
	0x55, 0x55, 0x49, 0x44, 0x18, 0x25, 0x20, 0x02, 0x28, 0x09, 0x52, 0x0a, 0x53, 0x79, 0x73, 0x6c,
	0x6f, 0x67, 0x55, 0x55, 0x49, 0x44, 0x12, 0x1a, 0x0a, 0x08, 0x57, 0x69, 0x66, 0x69, 0x4e, 0x61,
	0x6d, 0x65, 0x18, 0x26, 0x20, 0x02, 0x28, 0x09, 0x52, 0x08, 0x57, 0x69, 0x66, 0x69, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x57, 0x69, 0x66, 0x69, 0x4d, 0x61, 0x63, 0x18, 0x27, 0x20,
	0x02, 0x28, 0x09, 0x52, 0x07, 0x57, 0x69, 0x66, 0x69, 0x4d, 0x61, 0x63, 0x12, 0x18, 0x0a, 0x07,
	0x41, 0x70, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x28, 0x20, 0x02, 0x28, 0x09, 0x52, 0x07, 0x41,
	0x70, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x53, 0x73, 0x68, 0x50, 0x61, 0x74,
	0x68, 0x18, 0x29, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x53, 0x73, 0x68, 0x50, 0x61, 0x74, 0x68,
	0x12, 0x1a, 0x0a, 0x08, 0x54, 0x65, 0x6d, 0x70, 0x54, 0x65, 0x73, 0x74, 0x18, 0x2a, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x54, 0x65, 0x6d, 0x70, 0x54, 0x65, 0x73, 0x74, 0x12, 0x16, 0x0a, 0x06,
	0x44, 0x65, 0x76, 0x4d, 0x44, 0x35, 0x18, 0x2b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x44, 0x65,
	0x76, 0x4d, 0x44, 0x35, 0x12, 0x18, 0x0a, 0x07, 0x44, 0x65, 0x76, 0x55, 0x73, 0x65, 0x72, 0x18,
	0x2c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x44, 0x65, 0x76, 0x55, 0x73, 0x65, 0x72, 0x12, 0x1c,
	0x0a, 0x09, 0x44, 0x65, 0x76, 0x50, 0x72, 0x65, 0x66, 0x69, 0x78, 0x18, 0x2d, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x44, 0x65, 0x76, 0x50, 0x72, 0x65, 0x66, 0x69, 0x78, 0x12, 0x32, 0x0a, 0x0b,
	0x41, 0x70, 0x70, 0x46, 0x69, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x2e, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x10, 0x2e, 0x77, 0x65, 0x63, 0x68, 0x61, 0x74, 0x2e, 0x46, 0x69, 0x6c, 0x65, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x0b, 0x41, 0x70, 0x70, 0x46, 0x69, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x1c, 0x0a, 0x09, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x31, 0x32, 0x18, 0x2f, 0x20,
	0x02, 0x28, 0x09, 0x52, 0x09, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x31, 0x32, 0x12, 0x1a,
	0x0a, 0x08, 0x49, 0x73, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x18, 0x32, 0x20, 0x02, 0x28, 0x05,
	0x52, 0x08, 0x49, 0x73, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x12, 0x1c, 0x0a, 0x09, 0x4d, 0x6f,
	0x64, 0x69, 0x66, 0x79, 0x4d, 0x44, 0x35, 0x18, 0x33, 0x20, 0x02, 0x28, 0x09, 0x52, 0x09, 0x4d,
	0x6f, 0x64, 0x69, 0x66, 0x79, 0x4d, 0x44, 0x35, 0x12, 0x18, 0x0a, 0x07, 0x52, 0x71, 0x74, 0x48,
	0x61, 0x73, 0x68, 0x18, 0x34, 0x20, 0x02, 0x28, 0x03, 0x52, 0x07, 0x52, 0x71, 0x74, 0x48, 0x61,
	0x73, 0x68, 0x12, 0x1c, 0x0a, 0x09, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x31, 0x33, 0x18,
	0x35, 0x20, 0x02, 0x28, 0x05, 0x52, 0x09, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x31, 0x33,
	0x12, 0x1c, 0x0a, 0x09, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x31, 0x34, 0x18, 0x36, 0x20,
	0x02, 0x28, 0x05, 0x52, 0x09, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x31, 0x34, 0x12, 0x12,
	0x0a, 0x04, 0x53, 0x73, 0x69, 0x64, 0x18, 0x37, 0x20, 0x02, 0x28, 0x09, 0x52, 0x04, 0x53, 0x73,
	0x69, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x31, 0x35, 0x18,
	0x38, 0x20, 0x02, 0x28, 0x05, 0x52, 0x09, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x31, 0x35,
	0x12, 0x14, 0x0a, 0x05, 0x42, 0x73, 0x73, 0x69, 0x64, 0x18, 0x39, 0x20, 0x02, 0x28, 0x09, 0x52,
	0x05, 0x42, 0x73, 0x73, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x49, 0x73, 0x4a, 0x61, 0x69, 0x6c,
	0x18, 0x3a, 0x20, 0x02, 0x28, 0x05, 0x52, 0x06, 0x49, 0x73, 0x4a, 0x61, 0x69, 0x6c, 0x12, 0x12,
	0x0a, 0x04, 0x53, 0x65, 0x69, 0x64, 0x18, 0x3b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x53, 0x65,
	0x69, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x31, 0x36, 0x18,
	0x3c, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x31, 0x36,
	0x12, 0x1c, 0x0a, 0x09, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x31, 0x37, 0x18, 0x3d, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x09, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x31, 0x37, 0x12, 0x1c,
	0x0a, 0x09, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x31, 0x38, 0x18, 0x3e, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x09, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x31, 0x38, 0x12, 0x16, 0x0a, 0x06,
	0x57, 0x69, 0x66, 0x69, 0x4f, 0x6e, 0x18, 0x3f, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x57, 0x69,
	0x66, 0x69, 0x4f, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x42, 0x6c, 0x75, 0x65, 0x74, 0x68, 0x4f, 0x6e,
	0x18, 0x40, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x42, 0x6c, 0x75, 0x65, 0x74, 0x68, 0x4f, 0x6e,
	0x12, 0x1e, 0x0a, 0x0a, 0x42, 0x6c, 0x75, 0x65, 0x74, 0x68, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x41,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x42, 0x6c, 0x75, 0x65, 0x74, 0x68, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x1c, 0x0a, 0x09, 0x42, 0x6c, 0x75, 0x65, 0x74, 0x68, 0x4d, 0x61, 0x63, 0x18, 0x42, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x42, 0x6c, 0x75, 0x65, 0x74, 0x68, 0x4d, 0x61, 0x63, 0x12, 0x1c,
	0x0a, 0x09, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x31, 0x39, 0x18, 0x43, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x09, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x31, 0x39, 0x12, 0x1c, 0x0a, 0x09,
	0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x32, 0x30, 0x18, 0x44, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x09, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x32, 0x30, 0x12, 0x1c, 0x0a, 0x09, 0x55, 0x6e,
	0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x32, 0x36, 0x18, 0x45, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x55,
	0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x32, 0x36, 0x12, 0x16, 0x0a, 0x06, 0x48, 0x61, 0x73, 0x53,
	0x69, 0x6d, 0x18, 0x46, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x48, 0x61, 0x73, 0x53, 0x69, 0x6d,
	0x12, 0x1a, 0x0a, 0x08, 0x55, 0x73, 0x62, 0x53, 0x74, 0x61, 0x74, 0x65, 0x18, 0x47, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x08, 0x55, 0x73, 0x62, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x1c, 0x0a, 0x09,
	0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x32, 0x37, 0x18, 0x48, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x09, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x32, 0x37, 0x12, 0x1c, 0x0a, 0x09, 0x55, 0x6e,
	0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x32, 0x38, 0x18, 0x49, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x55,
	0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x32, 0x38, 0x12, 0x12, 0x0a, 0x04, 0x53, 0x69, 0x67, 0x6e,
	0x18, 0x4a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x53, 0x69, 0x67, 0x6e, 0x12, 0x20, 0x0a, 0x0b,
	0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x46, 0x6c, 0x61, 0x67, 0x18, 0x4b, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x0b, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x46, 0x6c, 0x61, 0x67, 0x12, 0x1e,
	0x0a, 0x0a, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x46, 0x6c, 0x61, 0x67, 0x18, 0x4c, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x0a, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x46, 0x6c, 0x61, 0x67, 0x12, 0x12,
	0x0a, 0x04, 0x49, 0x6d, 0x65, 0x69, 0x18, 0x4d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x49, 0x6d,
	0x65, 0x69, 0x12, 0x1c, 0x0a, 0x09, 0x44, 0x65, 0x76, 0x53, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x18,
	0x4e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x44, 0x65, 0x76, 0x53, 0x65, 0x72, 0x69, 0x61, 0x6c,
	0x12, 0x1c, 0x0a, 0x09, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x32, 0x39, 0x18, 0x4f, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x09, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x32, 0x39, 0x12, 0x1c,
	0x0a, 0x09, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x33, 0x30, 0x18, 0x50, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x09, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x33, 0x30, 0x12, 0x1c, 0x0a, 0x09,
	0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x33, 0x31, 0x18, 0x51, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x09, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x33, 0x31, 0x12, 0x1c, 0x0a, 0x09, 0x55, 0x6e,
	0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x33, 0x32, 0x18, 0x52, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x55,
	0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x33, 0x32, 0x12, 0x16, 0x0a, 0x06, 0x41, 0x70, 0x70, 0x4e,
	0x75, 0x6d, 0x18, 0x53, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x41, 0x70, 0x70, 0x4e, 0x75, 0x6d,
	0x12, 0x20, 0x0a, 0x0b, 0x54, 0x6f, 0x74, 0x63, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x18,
	0x54, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x54, 0x6f, 0x74, 0x63, 0x61, 0x70, 0x61, 0x63, 0x69,
	0x74, 0x79, 0x12, 0x20, 0x0a, 0x0b, 0x41, 0x76, 0x61, 0x63, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74,
	0x79, 0x18, 0x55, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x41, 0x76, 0x61, 0x63, 0x61, 0x70, 0x61,
	0x63, 0x69, 0x74, 0x79, 0x12, 0x1c, 0x0a, 0x09, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x33,
	0x33, 0x18, 0x56, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e,
	0x33, 0x33, 0x12, 0x1c, 0x0a, 0x09, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x33, 0x34, 0x18,
	0x57, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x33, 0x34,
	0x12, 0x1c, 0x0a, 0x09, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x33, 0x35, 0x18, 0x58, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x09, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x33, 0x35, 0x12, 0x1e,
	0x0a, 0x0a, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x31, 0x30, 0x33, 0x18, 0x59, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0a, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x31, 0x30, 0x33, 0x12, 0x1e,
	0x0a, 0x0a, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x31, 0x30, 0x34, 0x18, 0x5a, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0a, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x31, 0x30, 0x34, 0x12, 0x1e,
	0x0a, 0x0a, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x31, 0x30, 0x35, 0x18, 0x5b, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0a, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x31, 0x30, 0x35, 0x12, 0x1e,
	0x0a, 0x0a, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x31, 0x30, 0x36, 0x18, 0x5c, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x0a, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x31, 0x30, 0x36, 0x12, 0x1e,
	0x0a, 0x0a, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x31, 0x30, 0x37, 0x18, 0x5d, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0a, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x31, 0x30, 0x37, 0x12, 0x1e,
	0x0a, 0x0a, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x31, 0x30, 0x38, 0x18, 0x5e, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0a, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x31, 0x30, 0x38, 0x12, 0x1e,
	0x0a, 0x0a, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x31, 0x30, 0x39, 0x18, 0x5f, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0a, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x31, 0x30, 0x39, 0x12, 0x1e,
	0x0a, 0x0a, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x31, 0x31, 0x30, 0x18, 0x60, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0a, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x31, 0x31, 0x30, 0x12, 0x1e,
	0x0a, 0x0a, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x31, 0x31, 0x31, 0x18, 0x61, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0a, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x31, 0x31, 0x31, 0x12, 0x1e,
	0x0a, 0x0a, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x31, 0x31, 0x32, 0x18, 0x62, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x0a, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x31, 0x31, 0x32, 0x22, 0x6a,
	0x0a, 0x12, 0x4e, 0x65, 0x77, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x43, 0x68, 0x65, 0x63, 0x6b,
	0x44, 0x61, 0x74, 0x61, 0x12, 0x1a, 0x0a, 0x08, 0x43, 0x33, 0x32, 0x63, 0x44, 0x61, 0x74, 0x61,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x43, 0x33, 0x32, 0x63, 0x44, 0x61, 0x74, 0x61,
	0x12, 0x1c, 0x0a, 0x09, 0x54, 0x69, 0x6d, 0x65, 0x53, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x09, 0x54, 0x69, 0x6d, 0x65, 0x53, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x1a,
	0x0a, 0x08, 0x44, 0x61, 0x74, 0x61, 0x42, 0x6f, 0x64, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0c,
	0x52, 0x08, 0x44, 0x61, 0x74, 0x61, 0x42, 0x6f, 0x64, 0x79, 0x22, 0xbc, 0x01, 0x0a, 0x14, 0x44,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x75, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x49, 0x6e, 0x66, 0x6f,
	0x4e, 0x65, 0x77, 0x12, 0x18, 0x0a, 0x07, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x01,
	0x20, 0x02, 0x28, 0x0c, 0x52, 0x07, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a,
	0x04, 0x54, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x02, 0x28, 0x0d, 0x52, 0x04, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x20, 0x0a, 0x0b, 0x45, 0x6e, 0x63, 0x72, 0x79, 0x70, 0x74, 0x44, 0x61, 0x74, 0x61,
	0x18, 0x03, 0x20, 0x02, 0x28, 0x0c, 0x52, 0x0b, 0x45, 0x6e, 0x63, 0x72, 0x79, 0x70, 0x74, 0x44,
	0x61, 0x74, 0x61, 0x12, 0x1c, 0x0a, 0x09, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x18, 0x04, 0x20, 0x02, 0x28, 0x0d, 0x52, 0x09, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x12, 0x1a, 0x0a, 0x08, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x35, 0x18, 0x05, 0x20,
	0x02, 0x28, 0x0d, 0x52, 0x08, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x35, 0x12, 0x1a, 0x0a,
	0x08, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x36, 0x18, 0x06, 0x20, 0x02, 0x28, 0x0d, 0x52,
	0x08, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x36, 0x22, 0x73, 0x0a, 0x05, 0x57, 0x43, 0x53,
	0x54, 0x46, 0x12, 0x1c, 0x0a, 0x09, 0x53, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x02, 0x28, 0x04, 0x52, 0x09, 0x53, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65,
	0x12, 0x1c, 0x0a, 0x09, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20,
	0x02, 0x28, 0x04, 0x52, 0x09, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x14,
	0x0a, 0x05, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x02, 0x28, 0x0d, 0x52, 0x05, 0x43,
	0x6f, 0x75, 0x6e, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x45, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x18,
	0x04, 0x20, 0x03, 0x28, 0x04, 0x52, 0x07, 0x45, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x22, 0xb5,
	0x02, 0x0a, 0x05, 0x57, 0x43, 0x53, 0x54, 0x45, 0x12, 0x18, 0x0a, 0x07, 0x43, 0x68, 0x65, 0x63,
	0x6b, 0x49, 0x64, 0x18, 0x01, 0x20, 0x02, 0x28, 0x09, 0x52, 0x07, 0x43, 0x68, 0x65, 0x63, 0x6b,
	0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x53, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x02, 0x28, 0x0d, 0x52, 0x09, 0x53, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65,
	0x12, 0x1c, 0x0a, 0x09, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20,
	0x02, 0x28, 0x0d, 0x52, 0x09, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x16,
	0x0a, 0x06, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x31, 0x18, 0x04, 0x20, 0x02, 0x28, 0x0d, 0x52, 0x06,
	0x43, 0x6f, 0x75, 0x6e, 0x74, 0x31, 0x12, 0x16, 0x0a, 0x06, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x32,
	0x18, 0x05, 0x20, 0x02, 0x28, 0x0d, 0x52, 0x06, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x32, 0x12, 0x16,
	0x0a, 0x06, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x33, 0x18, 0x06, 0x20, 0x02, 0x28, 0x0d, 0x52, 0x06,
	0x43, 0x6f, 0x75, 0x6e, 0x74, 0x33, 0x12, 0x16, 0x0a, 0x06, 0x43, 0x6f, 0x6e, 0x73, 0x74, 0x31,
	0x18, 0x07, 0x20, 0x02, 0x28, 0x04, 0x52, 0x06, 0x43, 0x6f, 0x6e, 0x73, 0x74, 0x31, 0x12, 0x16,
	0x0a, 0x06, 0x43, 0x6f, 0x6e, 0x73, 0x74, 0x32, 0x18, 0x08, 0x20, 0x02, 0x28, 0x04, 0x52, 0x06,
	0x43, 0x6f, 0x6e, 0x73, 0x74, 0x32, 0x12, 0x16, 0x0a, 0x06, 0x43, 0x6f, 0x6e, 0x73, 0x74, 0x33,
	0x18, 0x09, 0x20, 0x02, 0x28, 0x04, 0x52, 0x06, 0x43, 0x6f, 0x6e, 0x73, 0x74, 0x33, 0x12, 0x16,
	0x0a, 0x06, 0x43, 0x6f, 0x6e, 0x73, 0x74, 0x34, 0x18, 0x0a, 0x20, 0x02, 0x28, 0x04, 0x52, 0x06,
	0x43, 0x6f, 0x6e, 0x73, 0x74, 0x34, 0x12, 0x16, 0x0a, 0x06, 0x43, 0x6f, 0x6e, 0x73, 0x74, 0x35,
	0x18, 0x0b, 0x20, 0x02, 0x28, 0x04, 0x52, 0x06, 0x43, 0x6f, 0x6e, 0x73, 0x74, 0x35, 0x12, 0x16,
	0x0a, 0x06, 0x43, 0x6f, 0x6e, 0x73, 0x74, 0x36, 0x18, 0x0c, 0x20, 0x02, 0x28, 0x04, 0x52, 0x06,
	0x43, 0x6f, 0x6e, 0x73, 0x74, 0x36, 0x22, 0xeb, 0x01, 0x0a, 0x07, 0x53, 0x61, 0x65, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c,
	0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x76, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0c, 0x52, 0x02, 0x69, 0x76, 0x12, 0x10, 0x0a, 0x03, 0x6c, 0x65, 0x6e, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x03, 0x6c, 0x65, 0x6e, 0x12, 0x22, 0x0a, 0x0c, 0x75, 0x6e, 0x6b, 0x6e,
	0x6f, 0x77, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x39, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0c,
	0x75, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x39, 0x12, 0x1a, 0x0a, 0x08,
	0x74, 0x61, 0x62, 0x6c, 0x65, 0x4b, 0x65, 0x79, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x08,
	0x74, 0x61, 0x62, 0x6c, 0x65, 0x4b, 0x65, 0x79, 0x12, 0x24, 0x0a, 0x0d, 0x75, 0x6e, 0x6b, 0x6e,
	0x6f, 0x77, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x31, 0x31, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0c, 0x52,
	0x0d, 0x75, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x31, 0x31, 0x12, 0x1e,
	0x0a, 0x0a, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x0c, 0x20, 0x01,
	0x28, 0x0c, 0x52, 0x0a, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x24,
	0x0a, 0x0d, 0x75, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x31, 0x38, 0x18,
	0x12, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0d, 0x75, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x31, 0x38, 0x22, 0x94, 0x02, 0x0a, 0x0d, 0x54, 0x65, 0x6e, 0x50, 0x61, 0x79, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x35, 0x0a, 0x0b, 0x62, 0x61, 0x73, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x77, 0x65,
	0x63, 0x68, 0x61, 0x74, 0x2e, 0x42, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x52, 0x0b, 0x62, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x16, 0x0a,
	0x06, 0x63, 0x67, 0x69, 0x43, 0x6d, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x63,
	0x67, 0x69, 0x43, 0x6d, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x6f, 0x75, 0x74, 0x50, 0x75, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x6f, 0x75, 0x74, 0x50, 0x75,
	0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x32, 0x0a, 0x07, 0x72, 0x65, 0x71, 0x54, 0x65, 0x78, 0x74,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x77, 0x65, 0x63, 0x68, 0x61, 0x74, 0x2e,
	0x53, 0x4b, 0x42, 0x75, 0x69, 0x6c, 0x74, 0x69, 0x6e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x5f,
	0x52, 0x07, 0x72, 0x65, 0x71, 0x54, 0x65, 0x78, 0x74, 0x12, 0x36, 0x0a, 0x09, 0x72, 0x65, 0x71,
	0x54, 0x65, 0x78, 0x74, 0x57, 0x78, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x77,
	0x65, 0x63, 0x68, 0x61, 0x74, 0x2e, 0x53, 0x4b, 0x42, 0x75, 0x69, 0x6c, 0x74, 0x69, 0x6e, 0x53,
	0x74, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x52, 0x09, 0x72, 0x65, 0x71, 0x54, 0x65, 0x78, 0x74, 0x57,
	0x78, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x67, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x73, 0x69, 0x67, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x72, 0x74, 0x4e, 0x6f, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x63, 0x72, 0x74, 0x4e, 0x6f, 0x22, 0x3f, 0x0a, 0x11, 0x53,
	0x4b, 0x42, 0x75, 0x69, 0x6c, 0x74, 0x69, 0x6e, 0x42, 0x75, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x74,
	0x12, 0x12, 0x0a, 0x04, 0x69, 0x4c, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04,
	0x69, 0x4c, 0x65, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x62, 0x75, 0x66, 0x66, 0x65, 0x72, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0c, 0x52, 0x06, 0x62, 0x75, 0x66, 0x66, 0x65, 0x72, 0x22, 0xf2, 0x02, 0x0a,
	0x16, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x41, 0x70, 0x70, 0x41, 0x74, 0x74, 0x61, 0x63, 0x68,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x35, 0x0a, 0x0b, 0x42, 0x61, 0x73, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x77,
	0x65, 0x63, 0x68, 0x61, 0x74, 0x2e, 0x42, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x52, 0x0b, 0x42, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x14,
	0x0a, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61,
	0x70, 0x70, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x73, 0x64, 0x6b, 0x56, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x73, 0x64, 0x6b, 0x56, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x12, 0x28, 0x0a, 0x0f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x41, 0x70,
	0x70, 0x44, 0x61, 0x74, 0x61, 0x49, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x63,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x41, 0x70, 0x70, 0x44, 0x61, 0x74, 0x61, 0x49, 0x64, 0x12, 0x1a,
	0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x74, 0x6f,
	0x74, 0x61, 0x6c, 0x4c, 0x65, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x74, 0x6f,
	0x74, 0x61, 0x6c, 0x4c, 0x65, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x72, 0x74, 0x50,
	0x6f, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x73, 0x74, 0x61, 0x72, 0x74, 0x50,
	0x6f, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x64, 0x61, 0x74, 0x61, 0x4c, 0x65, 0x6e, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x07, 0x64, 0x61, 0x74, 0x61, 0x4c, 0x65, 0x6e, 0x12, 0x2d, 0x0a, 0x04,
	0x64, 0x61, 0x74, 0x61, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x77, 0x65, 0x63,
	0x68, 0x61, 0x74, 0x2e, 0x53, 0x4b, 0x42, 0x75, 0x69, 0x6c, 0x74, 0x69, 0x6e, 0x42, 0x75, 0x66,
	0x66, 0x65, 0x72, 0x5f, 0x74, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x12, 0x0a, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12,
	0x10, 0x0a, 0x03, 0x6d, 0x64, 0x35, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x64,
	0x35, 0x22, 0xbb, 0x02, 0x0a, 0x17, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x41, 0x70, 0x70, 0x41,
	0x74, 0x74, 0x61, 0x63, 0x68, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x38, 0x0a,
	0x0c, 0x42, 0x61, 0x73, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x77, 0x65, 0x63, 0x68, 0x61, 0x74, 0x2e, 0x42, 0x61, 0x73,
	0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0c, 0x42, 0x61, 0x73, 0x65, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x18, 0x0a,
	0x07, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x6d, 0x65, 0x64, 0x69, 0x61, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x0f, 0x63, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x41, 0x70, 0x70, 0x44, 0x61, 0x74, 0x61, 0x49, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x41, 0x70, 0x70, 0x44, 0x61, 0x74, 0x61, 0x49,
	0x64, 0x12, 0x1a, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a,
	0x08, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x4c, 0x65, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x08, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x4c, 0x65, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x74, 0x61,
	0x72, 0x74, 0x50, 0x6f, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x73, 0x74, 0x61,
	0x72, 0x74, 0x50, 0x6f, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x64, 0x61, 0x74, 0x61, 0x4c, 0x65, 0x6e,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x64, 0x61, 0x74, 0x61, 0x4c, 0x65, 0x6e, 0x12,
	0x1e, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x22,
	0x97, 0x02, 0x0a, 0x14, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x56, 0x6f, 0x69, 0x63,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x73, 0x67, 0x49,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x6d, 0x73, 0x67, 0x49, 0x64, 0x12, 0x16,
	0x0a, 0x06, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06,
	0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x6c, 0x65, 0x6e, 0x67, 0x74, 0x68,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x6c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x12, 0x20,
	0x0a, 0x0b, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x4d, 0x73, 0x67, 0x49, 0x64, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x4d, 0x73, 0x67, 0x49, 0x64,
	0x12, 0x35, 0x0a, 0x0b, 0x62, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x77, 0x65, 0x63, 0x68, 0x61, 0x74, 0x2e, 0x42,
	0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x0b, 0x62, 0x61, 0x73, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x6e, 0x65, 0x77, 0x4d, 0x73,
	0x67, 0x49, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x6e, 0x65, 0x77, 0x4d, 0x73,
	0x67, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x0c, 0x63, 0x68, 0x61, 0x74, 0x52, 0x6f, 0x6f, 0x6d, 0x4e,
	0x61, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x68, 0x61, 0x74, 0x52,
	0x6f, 0x6f, 0x6d, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x6d, 0x61, 0x73, 0x74, 0x65,
	0x72, 0x42, 0x75, 0x66, 0x49, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x6d, 0x61,
	0x73, 0x74, 0x65, 0x72, 0x42, 0x75, 0x66, 0x49, 0x64, 0x22, 0xdf, 0x02, 0x0a, 0x15, 0x44, 0x6f,
	0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x56, 0x6f, 0x69, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x73, 0x67, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x05, 0x6d, 0x73, 0x67, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x66, 0x66,
	0x73, 0x65, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x6f, 0x66, 0x66, 0x73, 0x65,
	0x74, 0x12, 0x16, 0x0a, 0x06, 0x6c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x06, 0x6c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x12, 0x20, 0x0a, 0x0b, 0x76, 0x6f, 0x69,
	0x63, 0x65, 0x4c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0b,
	0x76, 0x6f, 0x69, 0x63, 0x65, 0x4c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x12, 0x20, 0x0a, 0x0b, 0x63,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x4d, 0x73, 0x67, 0x49, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x4d, 0x73, 0x67, 0x49, 0x64, 0x12, 0x2c, 0x0a,
	0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x77, 0x65,
	0x63, 0x68, 0x61, 0x74, 0x2e, 0x53, 0x4b, 0x42, 0x75, 0x69, 0x6c, 0x74, 0x69, 0x6e, 0x53, 0x74,
	0x72, 0x69, 0x6e, 0x67, 0x5f, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x18, 0x0a, 0x07, 0x65,
	0x6e, 0x64, 0x46, 0x6c, 0x61, 0x67, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x65, 0x6e,
	0x64, 0x46, 0x6c, 0x61, 0x67, 0x12, 0x38, 0x0a, 0x0c, 0x62, 0x61, 0x73, 0x65, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x77, 0x65,
	0x63, 0x68, 0x61, 0x74, 0x2e, 0x42, 0x61, 0x73, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x52, 0x0c, 0x62, 0x61, 0x73, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x1e, 0x0a, 0x0a, 0x63, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x46, 0x6c, 0x61, 0x67, 0x18, 0x0a, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x0a, 0x63, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x46, 0x6c, 0x61, 0x67, 0x12,
	0x1a, 0x0a, 0x08, 0x6e, 0x65, 0x77, 0x4d, 0x73, 0x67, 0x49, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x08, 0x6e, 0x65, 0x77, 0x4d, 0x73, 0x67, 0x49, 0x64, 0x42, 0x0a, 0x5a, 0x08, 0x2f,
	0x3b, 0x77, 0x65, 0x63, 0x68, 0x61, 0x74,
}

var (
	file_wechat_proto_rawDescOnce sync.Once
	file_wechat_proto_rawDescData = file_wechat_proto_rawDesc
)

func file_wechat_proto_rawDescGZIP() []byte {
	file_wechat_proto_rawDescOnce.Do(func() {
		file_wechat_proto_rawDescData = protoimpl.X.CompressGZIP(file_wechat_proto_rawDescData)
	})
	return file_wechat_proto_rawDescData
}

var file_wechat_proto_msgTypes = make([]protoimpl.MessageInfo, 39)
var file_wechat_proto_goTypes = []interface{}{
	(*BaseRequest)(nil),                     // 0: wechat.BaseRequest
	(*BaseRequestPlus)(nil),                 // 1: wechat.BaseRequestPlus
	(*SKBuiltinStringT)(nil),                // 2: wechat.SKBuiltinString_t
	(*BaseResponse)(nil),                    // 3: wechat.BaseResponse
	(*MMBizJsApiGetUserOpenIdRequest)(nil),  // 4: wechat.MMBizJsApiGetUserOpenIdRequest
	(*MMBizJsApiGetUserOpenIdResponse)(nil), // 5: wechat.MMBizJsApiGetUserOpenIdResponse
	(*BufferT)(nil),                         // 6: wechat.Buffer_t
	(*EcdhPacket)(nil),                      // 7: wechat.EcdhPacket
	(*HybridDecryptResponse)(nil),           // 8: wechat.HybridDecryptResponse
	(*HybridEcdhRequest)(nil),               // 9: wechat.HybridEcdhRequest
	(*HybridEcdhResponse)(nil),              // 10: wechat.HybridEcdhResponse
	(*ECDHKey)(nil),                         // 11: wechat.ECDHKey
	(*SKBuiltinString_)(nil),                // 12: wechat.SKBuiltinString_
	(*ManualAuthRsaReqData)(nil),            // 13: wechat.ManualAuthRsaReqData
	(*WTLoginImgReqInfo)(nil),               // 14: wechat.WTLoginImgReqInfo
	(*WxVerifyCodeReqInfo)(nil),             // 15: wechat.WxVerifyCodeReqInfo
	(*BaseAuthReqInfo)(nil),                 // 16: wechat.BaseAuthReqInfo
	(*ManualAuthAesReqData)(nil),            // 17: wechat.ManualAuthAesReqData
	(*TrustSoftData)(nil),                   // 18: wechat.TrustSoftData
	(*TrustResponseData)(nil),               // 19: wechat.TrustResponseData
	(*TrustResp)(nil),                       // 20: wechat.TrustResp
	(*AutoAuthKey)(nil),                     // 21: wechat.AutoAuthKey
	(*AutoAuthRsaReqData)(nil),              // 22: wechat.AutoAuthRsaReqData
	(*AutoAuthAesReqData)(nil),              // 23: wechat.AutoAuthAesReqData
	(*AutoAuthRequest)(nil),                 // 24: wechat.AutoAuthRequest
	(*WCExtInfoNew)(nil),                    // 25: wechat.WCExtInfoNew
	(*FileInfo)(nil),                        // 26: wechat.FileInfo
	(*SpamDataBody)(nil),                    // 27: wechat.SpamDataBody
	(*NewClientCheckData)(nil),              // 28: wechat.NewClientCheckData
	(*DeviceRunningInfoNew)(nil),            // 29: wechat.DeviceRunningInfoNew
	(*WCSTF)(nil),                           // 30: wechat.WCSTF
	(*WCSTE)(nil),                           // 31: wechat.WCSTE
	(*SaeInfo)(nil),                         // 32: wechat.SaeInfo
	(*TenPayRequest)(nil),                   // 33: wechat.TenPayRequest
	(*SKBuiltinBufferT)(nil),                // 34: wechat.SKBuiltinBuffer_t
	(*UploadAppAttachRequest)(nil),          // 35: wechat.UploadAppAttachRequest
	(*UploadAppAttachResponse)(nil),         // 36: wechat.UploadAppAttachResponse
	(*DownloadVoiceRequest)(nil),            // 37: wechat.DownloadVoiceRequest
	(*DownloadVoiceResponse)(nil),           // 38: wechat.DownloadVoiceResponse
}
var file_wechat_proto_depIdxs = []int32{
	2,  // 0: wechat.BaseResponse.errMsg:type_name -> wechat.SKBuiltinString_t
	0,  // 1: wechat.MMBizJsApiGetUserOpenIdRequest.BaseRequest:type_name -> wechat.BaseRequest
	3,  // 2: wechat.MMBizJsApiGetUserOpenIdResponse.BaseResponse:type_name -> wechat.BaseResponse
	6,  // 3: wechat.EcdhPacket.Key:type_name -> wechat.Buffer_t
	6,  // 4: wechat.HybridDecryptResponse.Key:type_name -> wechat.Buffer_t
	6,  // 5: wechat.HybridEcdhRequest.SecECDHKey:type_name -> wechat.Buffer_t
	6,  // 6: wechat.HybridEcdhResponse.SecECDHKey:type_name -> wechat.Buffer_t
	12, // 7: wechat.ECDHKey.key:type_name -> wechat.SKBuiltinString_
	12, // 8: wechat.ManualAuthRsaReqData.randomEncryKey:type_name -> wechat.SKBuiltinString_
	11, // 9: wechat.ManualAuthRsaReqData.cliPubEcdhkey:type_name -> wechat.ECDHKey
	12, // 10: wechat.WTLoginImgReqInfo.ksid:type_name -> wechat.SKBuiltinString_
	12, // 11: wechat.BaseAuthReqInfo.wt_login_req_buff:type_name -> wechat.SKBuiltinString_
	14, // 12: wechat.BaseAuthReqInfo.wt_login_img_req_info:type_name -> wechat.WTLoginImgReqInfo
	15, // 13: wechat.BaseAuthReqInfo.wx_verify_code_req_info:type_name -> wechat.WxVerifyCodeReqInfo
	12, // 14: wechat.BaseAuthReqInfo.clidb_encrypt_key:type_name -> wechat.SKBuiltinString_
	12, // 15: wechat.BaseAuthReqInfo.clidb_encrypt_info:type_name -> wechat.SKBuiltinString_
	0,  // 16: wechat.ManualAuthAesReqData.baseRequest:type_name -> wechat.BaseRequest
	16, // 17: wechat.ManualAuthAesReqData.baseReqInfo:type_name -> wechat.BaseAuthReqInfo
	12, // 18: wechat.ManualAuthAesReqData.clientCheckData:type_name -> wechat.SKBuiltinString_
	12, // 19: wechat.ManualAuthAesReqData.extSpamInfo:type_name -> wechat.SKBuiltinString_
	18, // 20: wechat.TrustResponseData.SoftData:type_name -> wechat.TrustSoftData
	3,  // 21: wechat.TrustResp.BaseResponse:type_name -> wechat.BaseResponse
	19, // 22: wechat.TrustResp.TrustResponseData:type_name -> wechat.TrustResponseData
	6,  // 23: wechat.AutoAuthKey.EncryptKey:type_name -> wechat.Buffer_t
	6,  // 24: wechat.AutoAuthKey.Key:type_name -> wechat.Buffer_t
	12, // 25: wechat.AutoAuthRsaReqData.aes_encrypt_key:type_name -> wechat.SKBuiltinString_
	11, // 26: wechat.AutoAuthRsaReqData.pubEcdhKey:type_name -> wechat.ECDHKey
	1,  // 27: wechat.AutoAuthAesReqData.base_request:type_name -> wechat.BaseRequestPlus
	16, // 28: wechat.AutoAuthAesReqData.base_req_info:type_name -> wechat.BaseAuthReqInfo
	12, // 29: wechat.AutoAuthAesReqData.auto_auth_key:type_name -> wechat.SKBuiltinString_
	12, // 30: wechat.AutoAuthAesReqData.clientCheckData:type_name -> wechat.SKBuiltinString_
	12, // 31: wechat.AutoAuthAesReqData.extSpamInfo:type_name -> wechat.SKBuiltinString_
	22, // 32: wechat.AutoAuthRequest.rsa_req_data:type_name -> wechat.AutoAuthRsaReqData
	23, // 33: wechat.AutoAuthRequest.aes_req_data:type_name -> wechat.AutoAuthAesReqData
	6,  // 34: wechat.WCExtInfoNew.Wcstf:type_name -> wechat.Buffer_t
	6,  // 35: wechat.WCExtInfoNew.Wcste:type_name -> wechat.Buffer_t
	6,  // 36: wechat.WCExtInfoNew.CcData:type_name -> wechat.Buffer_t
	6,  // 37: wechat.WCExtInfoNew.UserAttrInfo:type_name -> wechat.Buffer_t
	6,  // 38: wechat.WCExtInfoNew.AcgiDeviceInfo:type_name -> wechat.Buffer_t
	6,  // 39: wechat.WCExtInfoNew.AcgiTuring:type_name -> wechat.Buffer_t
	6,  // 40: wechat.WCExtInfoNew.DeviceToken:type_name -> wechat.Buffer_t
	6,  // 41: wechat.WCExtInfoNew.BehaviorId:type_name -> wechat.Buffer_t
	6,  // 42: wechat.WCExtInfoNew.IosturingHuman:type_name -> wechat.Buffer_t
	6,  // 43: wechat.WCExtInfoNew.IosturingOwner:type_name -> wechat.Buffer_t
	26, // 44: wechat.SpamDataBody.AppFileInfo:type_name -> wechat.FileInfo
	0,  // 45: wechat.TenPayRequest.baseRequest:type_name -> wechat.BaseRequest
	12, // 46: wechat.TenPayRequest.reqText:type_name -> wechat.SKBuiltinString_
	12, // 47: wechat.TenPayRequest.reqTextWx:type_name -> wechat.SKBuiltinString_
	0,  // 48: wechat.UploadAppAttachRequest.BaseRequest:type_name -> wechat.BaseRequest
	34, // 49: wechat.UploadAppAttachRequest.data:type_name -> wechat.SKBuiltinBuffer_t
	3,  // 50: wechat.UploadAppAttachResponse.BaseResponse:type_name -> wechat.BaseResponse
	0,  // 51: wechat.DownloadVoiceRequest.baseRequest:type_name -> wechat.BaseRequest
	12, // 52: wechat.DownloadVoiceResponse.data:type_name -> wechat.SKBuiltinString_
	3,  // 53: wechat.DownloadVoiceResponse.baseResponse:type_name -> wechat.BaseResponse
	54, // [54:54] is the sub-list for method output_type
	54, // [54:54] is the sub-list for method input_type
	54, // [54:54] is the sub-list for extension type_name
	54, // [54:54] is the sub-list for extension extendee
	0,  // [0:54] is the sub-list for field type_name
}

func init() { file_wechat_proto_init() }
func file_wechat_proto_init() {
	if File_wechat_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_wechat_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BaseRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wechat_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BaseRequestPlus); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wechat_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SKBuiltinStringT); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wechat_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BaseResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wechat_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MMBizJsApiGetUserOpenIdRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wechat_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MMBizJsApiGetUserOpenIdResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wechat_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BufferT); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wechat_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EcdhPacket); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wechat_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HybridDecryptResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wechat_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HybridEcdhRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wechat_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HybridEcdhResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wechat_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ECDHKey); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wechat_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SKBuiltinString_); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wechat_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ManualAuthRsaReqData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wechat_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WTLoginImgReqInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wechat_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WxVerifyCodeReqInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wechat_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BaseAuthReqInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wechat_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ManualAuthAesReqData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wechat_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TrustSoftData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wechat_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TrustResponseData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wechat_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TrustResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wechat_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AutoAuthKey); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wechat_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AutoAuthRsaReqData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wechat_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AutoAuthAesReqData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wechat_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AutoAuthRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wechat_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WCExtInfoNew); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wechat_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FileInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wechat_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SpamDataBody); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wechat_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NewClientCheckData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wechat_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeviceRunningInfoNew); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wechat_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WCSTF); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wechat_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WCSTE); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wechat_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SaeInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wechat_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TenPayRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wechat_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SKBuiltinBufferT); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wechat_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UploadAppAttachRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wechat_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UploadAppAttachResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wechat_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DownloadVoiceRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wechat_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DownloadVoiceResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_wechat_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   39,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_wechat_proto_goTypes,
		DependencyIndexes: file_wechat_proto_depIdxs,
		MessageInfos:      file_wechat_proto_msgTypes,
	}.Build()
	File_wechat_proto = out.File
	file_wechat_proto_rawDesc = nil
	file_wechat_proto_goTypes = nil
	file_wechat_proto_depIdxs = nil
}
