package main

import (
	"fmt"
	"github.com/astaxie/beego"
	"log"
	"runtime"
	"wechatdll/TcpPoll"
	"wechatdll/comm"
	_ "wechatdll/routers"
)

func main() {
	// 增加全局recover处理
	defer func() {
		if r := recover(); r != nil {
			log.Printf("捕获到panic: %v", r)
		}
	}()

	longLinkEnabled, _ := beego.AppConfig.Bool("longlinkenabled")

	comm.RedisInitialize()
	_, err := comm.RedisClient.Ping().Result()
	if err != nil {
		log.Fatalf("【Redis】连接失败，ERROR：%v", err)
	}
	fmt.Println("By 猛牛科技")

	sysType := runtime.GOOS

	if sysType == "linux" && longLinkEnabled {
		// LINUX系统
		tcpManager, err := TcpPoll.GetTcpManager()
		if err != nil {
			log.Fatalf("TCP启动失败: %v", err)
		}
		go tcpManager.RunEventLoop()
	}

	beego.BConfig.WebConfig.DirectoryIndex = true
	beego.BConfig.WebConfig.StaticDir["/"] = "swagger"

	beego.BeeLogger.SetLogger("file", `{"filename":"app.log"}`)

	// 启动
	beego.Run()
}
